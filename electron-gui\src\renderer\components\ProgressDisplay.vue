<template>
  <el-card class="progress-card modern-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <div class="header-left">
          <el-icon class="header-icon"><TrendCharts /></el-icon>
          <span class="header-title">下载进度</span>
        </div>
        <div class="header-actions">
          <el-tag v-if="isDownloading" type="success" effect="light" class="status-tag">
            <el-icon class="rotating"><Loading /></el-icon>
            下载中
          </el-tag>
          <el-tag v-else type="info" effect="light" class="status-tag">
            <el-icon><Clock /></el-icon>
            待机中
          </el-tag>
        </div>
      </div>
    </template>
    
    <div class="progress-content">
      <!-- 主进度条 -->
      <div class="main-progress">
        <div class="progress-info">
          <span class="progress-text">
            总进度: {{ progress.current }} / {{ progress.total }}
          </span>
          <span class="progress-percentage">
            {{ progress.percentage.toFixed(1) }}%
          </span>
        </div>
        
        <el-progress
          :percentage="progress.percentage"
          :status="progressStatus"
          :stroke-width="12"
          :show-text="false"
          class="modern-progress-bar"
        />
      </div>
      
      <!-- 详细信息 -->
      <div class="progress-details">
        <div class="detail-item">
          <el-icon><Download /></el-icon>
          <span class="detail-label">当前任务:</span>
          <span class="detail-value">{{ currentTask || '无' }}</span>
        </div>
        
        <div class="detail-item">
          <el-icon><Timer /></el-icon>
          <span class="detail-label">已用时间:</span>
          <span class="detail-value">{{ formatDuration(elapsedTime) }}</span>
        </div>
        
        <div class="detail-item">
          <el-icon><Clock /></el-icon>
          <span class="detail-label">预计剩余:</span>
          <span class="detail-value">{{ formatDuration(estimatedTime) }}</span>
        </div>
        
        <div class="detail-item">
          <el-icon><Odometer /></el-icon>
          <span class="detail-label">下载速度:</span>
          <span class="detail-value">{{ downloadSpeed }}</span>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script>
import { mapState } from 'vuex'
import { TrendCharts, Loading, Download, Timer, Clock, Odometer } from '@element-plus/icons-vue'

export default {
  name: 'ProgressDisplay',
  components: {
    TrendCharts,
    Loading,
    Download,
    Timer,
    Clock,
    Odometer
  },
  data() {
    return {
      startTime: null,
      elapsedTime: 0,
      timer: null,
      currentTask: '',
      downloadSpeed: '0 张/分钟'
    }
  },
  computed: {
    ...mapState(['isDownloading', 'downloadProgress']),
    
    progress() {
      // 确保downloadProgress是对象而不是数字
      const progressData = typeof this.downloadProgress === 'object' ? this.downloadProgress : {}
      const result = {
        current: progressData.current || 0,
        total: progressData.total || 0,
        percentage: progressData.percentage || 0,
        message: progressData.message || ''
      }
      console.log('🎯 ProgressDisplay计算属性:', result, '原始数据:', this.downloadProgress)
      return result
    },
    
    progressStatus() {
      if (!this.isDownloading) return 'normal'
      if (this.progress.percentage === 100) return 'success'
      if (this.progress.percentage > 0) return 'normal'
      return 'normal'
    },
    
    estimatedTime() {
      if (!this.isDownloading || this.progress.current === 0) return 0
      
      const rate = this.progress.current / (this.elapsedTime / 1000)
      const remaining = this.progress.total - this.progress.current
      
      return remaining / rate * 1000
    }
  },
  
  methods: {
    formatDuration(milliseconds) {
      if (!milliseconds || milliseconds <= 0) return '00:00:00'
      
      const seconds = Math.floor(milliseconds / 1000)
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = seconds % 60
      
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    },
    
    updateElapsedTime() {
      if (this.startTime && this.isDownloading) {
        this.elapsedTime = Date.now() - this.startTime
        
        // 计算下载速度
        if (this.elapsedTime > 0) {
          const rate = (this.progress.current / (this.elapsedTime / 1000)) * 60 // 每分钟
          this.downloadSpeed = `${rate.toFixed(1)} 张/分钟`
        }
      }
    },
    
    startTimer() {
      this.startTime = Date.now()
      this.timer = setInterval(this.updateElapsedTime, 1000)
    },
    
    stopTimer() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },
    
    resetProgress() {
      this.startTime = null
      this.elapsedTime = 0
      this.currentTask = ''
      this.downloadSpeed = '0 张/分钟'
      this.stopTimer()
    },

    updateCurrentTask() {
      if (this.isDownloading && this.progress.current > 0) {
        if (this.progress.message) {
          this.currentTask = this.progress.message
        } else {
          this.currentTask = `正在下载第 ${this.progress.current} 张图片...`
        }
      } else {
        this.currentTask = ''
      }
    }
  },
  
  watch: {
    isDownloading(newVal) {
      if (newVal) {
        this.startTimer()
      } else {
        this.stopTimer()
      }
    },

    // 监听进度变化，更新当前任务
    'progress.current'() {
      this.updateCurrentTask()
    },

    'progress.message'() {
      this.updateCurrentTask()
    }
  },
  
  mounted() {
    // 实时更新当前任务信息
    this.updateCurrentTask()
  },

  beforeUnmount() {
    this.stopTimer()
  }
}
</script>

<style scoped>
.progress-card {
  margin-bottom: 24px;
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-md) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  overflow: hidden;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%) !important;
}

.progress-card:hover {
  box-shadow: var(--shadow-lg) !important;
  transform: translateY(-2px) !important;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
  padding: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 20px;
  color: var(--primary-color);
  background: var(--primary-light);
  padding: 8px;
  border-radius: var(--radius-lg);
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: 12px;
}

.status-tag {
  border-radius: var(--radius-lg) !important;
  font-weight: 500 !important;
  padding: 6px 12px !important;
  border: none !important;
  box-shadow: var(--shadow-xs) !important;
}

.progress-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.main-progress {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-text {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.progress-percentage {
  font-size: 20px;
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.progress-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.detail-item:hover {
  background: var(--bg-tertiary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.detail-item .el-icon {
  font-size: 16px;
  color: var(--primary-color);
  background: var(--primary-light);
  padding: 6px;
  border-radius: var(--radius-md);
}

.detail-label {
  font-size: 13px;
  color: var(--text-secondary);
  font-weight: 500;
  min-width: 70px;
}

.detail-value {
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 600;
  flex: 1;
  text-align: right;
}

.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 现代化进度条样式 */
.modern-progress-bar .el-progress-bar__outer {
  background-color: var(--bg-tertiary) !important;
  border-radius: var(--radius-full) !important;
  overflow: hidden !important;
  height: 12px !important;
  box-shadow: var(--shadow-inner) !important;
}

.modern-progress-bar .el-progress-bar__inner {
  background: var(--gradient-primary) !important;
  border-radius: var(--radius-full) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
}

.modern-progress-bar .el-progress-bar__inner::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
  animation: progress-shine 2s ease-in-out infinite;
  border-radius: var(--radius-full);
}

@keyframes progress-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@media (max-width: 768px) {
  .progress-details {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .main-progress {
    padding: 16px;
    gap: 12px;
  }

  .detail-item {
    padding: 12px;
  }

  .header-left {
    gap: 8px;
  }

  .header-icon {
    font-size: 18px;
    padding: 6px;
  }

  .header-title {
    font-size: 15px;
  }

  .progress-content {
    gap: 20px;
  }
}
</style>
