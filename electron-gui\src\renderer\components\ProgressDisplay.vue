<template>
  <el-card class="progress-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <el-icon><TrendCharts /></el-icon>
        <span>下载进度</span>
        <div class="header-actions">
          <el-tag v-if="isDownloading" type="success" effect="plain">
            <el-icon><Loading /></el-icon>
            下载中
          </el-tag>
          <el-tag v-else type="info" effect="plain">
            待机中
          </el-tag>
        </div>
      </div>
    </template>
    
    <div class="progress-content">
      <!-- 主进度条 -->
      <div class="main-progress">
        <div class="progress-info">
          <span class="progress-text">
            总进度: {{ progress.current }} / {{ progress.total }}
          </span>
          <span class="progress-percentage">
            {{ progress.percentage.toFixed(1) }}%
          </span>
        </div>
        
        <el-progress 
          :percentage="progress.percentage" 
          :status="progressStatus"
          :stroke-width="12"
          :show-text="false"
        />
      </div>
      
      <!-- 详细信息 -->
      <div class="progress-details">
        <div class="detail-item">
          <el-icon><Download /></el-icon>
          <span class="detail-label">当前任务:</span>
          <span class="detail-value">{{ currentTask || '无' }}</span>
        </div>
        
        <div class="detail-item">
          <el-icon><Timer /></el-icon>
          <span class="detail-label">已用时间:</span>
          <span class="detail-value">{{ formatDuration(elapsedTime) }}</span>
        </div>
        
        <div class="detail-item">
          <el-icon><Clock /></el-icon>
          <span class="detail-label">预计剩余:</span>
          <span class="detail-value">{{ formatDuration(estimatedTime) }}</span>
        </div>
        
        <div class="detail-item">
          <el-icon><Odometer /></el-icon>
          <span class="detail-label">下载速度:</span>
          <span class="detail-value">{{ downloadSpeed }}</span>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script>
import { mapState } from 'vuex'
import { TrendCharts, Loading, Download, Timer, Clock, Odometer } from '@element-plus/icons-vue'

export default {
  name: 'ProgressDisplay',
  components: {
    TrendCharts,
    Loading,
    Download,
    Timer,
    Clock,
    Odometer
  },
  data() {
    return {
      startTime: null,
      elapsedTime: 0,
      timer: null,
      currentTask: '',
      downloadSpeed: '0 张/分钟'
    }
  },
  computed: {
    ...mapState(['isDownloading', 'downloadProgress']),
    
    progress() {
      // 确保downloadProgress是对象而不是数字
      const progressData = typeof this.downloadProgress === 'object' ? this.downloadProgress : {}
      const result = {
        current: progressData.current || 0,
        total: progressData.total || 0,
        percentage: progressData.percentage || 0,
        message: progressData.message || ''
      }
      console.log('🎯 ProgressDisplay计算属性:', result, '原始数据:', this.downloadProgress)
      return result
    },
    
    progressStatus() {
      if (!this.isDownloading) return 'normal'
      if (this.progress.percentage === 100) return 'success'
      if (this.progress.percentage > 0) return 'normal'
      return 'normal'
    },
    
    estimatedTime() {
      if (!this.isDownloading || this.progress.current === 0) return 0
      
      const rate = this.progress.current / (this.elapsedTime / 1000)
      const remaining = this.progress.total - this.progress.current
      
      return remaining / rate * 1000
    }
  },
  
  methods: {
    formatDuration(milliseconds) {
      if (!milliseconds || milliseconds <= 0) return '00:00:00'
      
      const seconds = Math.floor(milliseconds / 1000)
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = seconds % 60
      
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    },
    
    updateElapsedTime() {
      if (this.startTime && this.isDownloading) {
        this.elapsedTime = Date.now() - this.startTime
        
        // 计算下载速度
        if (this.elapsedTime > 0) {
          const rate = (this.progress.current / (this.elapsedTime / 1000)) * 60 // 每分钟
          this.downloadSpeed = `${rate.toFixed(1)} 张/分钟`
        }
      }
    },
    
    startTimer() {
      this.startTime = Date.now()
      this.timer = setInterval(this.updateElapsedTime, 1000)
    },
    
    stopTimer() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },
    
    resetProgress() {
      this.startTime = null
      this.elapsedTime = 0
      this.currentTask = ''
      this.downloadSpeed = '0 张/分钟'
      this.stopTimer()
    },

    updateCurrentTask() {
      if (this.isDownloading && this.progress.current > 0) {
        if (this.progress.message) {
          this.currentTask = this.progress.message
        } else {
          this.currentTask = `正在下载第 ${this.progress.current} 张图片...`
        }
      } else {
        this.currentTask = ''
      }
    }
  },
  
  watch: {
    isDownloading(newVal) {
      if (newVal) {
        this.startTimer()
      } else {
        this.stopTimer()
      }
    },

    // 监听进度变化，更新当前任务
    'progress.current'() {
      this.updateCurrentTask()
    },

    'progress.message'() {
      this.updateCurrentTask()
    }
  },
  
  mounted() {
    // 实时更新当前任务信息
    this.updateCurrentTask()
  },

  beforeUnmount() {
    this.stopTimer()
  }
}
</script>

<style scoped>
.progress-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
}

.card-header > div:first-child {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.main-progress {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-text {
  font-size: 14px;
  color: var(--text-secondary);
}

.progress-percentage {
  font-size: 18px;
  font-weight: 600;
  color: var(--accent-color);
}

.progress-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px;
  background: var(--bg-secondary);
  border-radius: 6px;
}

.detail-label {
  font-size: 13px;
  color: var(--text-muted);
  min-width: 60px;
}

.detail-value {
  font-size: 13px;
  color: var(--text-primary);
  font-weight: 500;
  flex: 1;
  text-align: right;
}

@media (max-width: 768px) {
  .progress-details {
    grid-template-columns: 1fr;
  }
}
</style>
