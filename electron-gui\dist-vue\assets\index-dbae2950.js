import{al as c,y as m,z as P,Q as s,H as p,I as a,A as l,M as f,L as T,O as S,ax as V,C as K,K as ae,P as F,a4 as le,D as oe,ay as Re,az as Ce,aA as be,au as Te}from"./vue-vendor-6f0eeaa0.js";import{l as Y,m as ie,s as B,c as J,i as q,a as Ee,b as re,r as de,E as Pe,d as O,e as Z,f as ce,g as x,u as De,k as Ie,w as H,h as N,j as ee,v as ue,n as ge,o as Le,p as he,q as me,t as _e,x as Oe,y as Ae,z as pe,A as fe,B as te,C as Me,D as se,F as we,G as Se,H as Ve,I as xe,J as Ne,K as ye,L as Ue,M as $e,N as ze,O as Fe,P as We,Q as Be,R as Ge,S as Ke,T as He,U as Ye,V as Je,W as Qe,X as je,Y as ne,Z as ve,_ as Xe,$ as ke,a0 as qe,a1 as Ze,a2 as et,a3 as tt,a4 as st,a5 as ot,a6 as nt,a7 as at,a8 as lt}from"./element-plus-a58f6e40.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))r(n);new MutationObserver(n=>{for(const i of n)if(i.type==="childList")for(const y of i.addedNodes)y.tagName==="LINK"&&y.rel==="modulepreload"&&r(y)}).observe(document,{childList:!0,subtree:!0});function o(n){const i={};return n.integrity&&(i.integrity=n.integrity),n.referrerPolicy&&(i.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?i.credentials="include":n.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(n){if(n.ep)return;n.ep=!0;const i=o(n);fetch(n.href,i)}})();const I=(t,e)=>{const o=t.__vccOpts||t;for(const[r,n]of e)o[r]=n;return o},it={name:"App",mounted(){this.$store.dispatch("loadSettings"),window.electronAPI&&(window.electronAPI.onPythonInfo((t,e)=>{this.$store.commit("addLog",e.trim())}),window.electronAPI.onPythonWarning((t,e)=>{this.$store.commit("addLog",`⚠️ ${e.trim()}`)}),window.electronAPI.onPythonError((t,e)=>{this.$store.commit("addLog",`❌ ${e.trim()}`)}),window.electronAPI.onPythonLog((t,e)=>{this.$store.commit("addLog",e.trim())}),window.electronAPI.onLogMessage((t,e)=>{const o=`[${e.level}] ${e.message}`;this.$store.commit("addLog",o)}),window.electronAPI.onDownloadComplete((t,e)=>{if(console.log("🎉 收到下载完成事件:",e),this.$store.commit("setDownloading",!1),this.$store.commit("addLog","🎉 下载任务已完成！"),e&&e.stats){const o=e.stats,r=`下载完成 - 成功: ${o.success||0}, 跳过: ${o.skipped||0}, 失败: ${o.failed||0}`;this.$store.commit("addLog",`📊 ${r}`),this.$store.commit("updateDownloadState",{stats:{total:o.total||0,completed:o.success||0,failed:o.failed||0,skipped:o.skipped||0}})}}),window.electronAPI.onProgressUpdate((t,e)=>{console.log("📊 收到进度更新:",e),console.log("📊 当前store状态:",this.$store.state.downloadProgress,this.$store.state.downloadStats),this.$store.commit("updateDownloadState",{progress:{current:e.current||0,total:e.total||0,percentage:e.percentage||0,message:e.message||""},stats:e.stats||{}}),console.log("📊 更新后store状态:",this.$store.state.downloadProgress,this.$store.state.downloadStats)}),console.log("🧪 添加测试进度更新功能"),window.testProgressUpdate=()=>{console.log("🧪 模拟进度更新..."),this.$store.commit("updateDownloadState",{progress:{current:5,total:10,percentage:50,message:"测试进度更新"},stats:{success:3,failed:1,skipped:1,total:10}}),console.log("🧪 测试进度更新完成")})},beforeUnmount(){window.electronAPI&&(window.electronAPI.removeAllListeners("python-log"),window.electronAPI.removeAllListeners("python-error"),window.electronAPI.removeAllListeners("python-info"),window.electronAPI.removeAllListeners("python-warning"),window.electronAPI.removeAllListeners("log-message"))}},rt={id:"app"};function dt(t,e,o,r,n,i){const y=c("router-view");return m(),P("div",rt,[s(y)])}const ct=I(it,[["render",dt]]);const ut={name:"LoginDialog",inject:["$api"],components:{Loading:Y,Monitor:ie,SuccessFilled:B,CircleCloseFilled:J,InfoFilled:q,Check:Ee,Close:re,Refresh:de},props:{modelValue:{type:Boolean,default:!1}},emits:["update:modelValue","login-success","login-failed","login-cancelled"],data(){return{loginStatus:"starting",statusMessage:"正在检查登录状态...",errorMessage:"",confirming:!1,progressPercentage:0,forceRelogin:!1}},computed:{visible:{get(){return this.modelValue},set(t){this.$emit("update:modelValue",t)}},statusTitle(){return{starting:"正在启动登录流程",checking:"检查现有登录状态",selenium_starting:"正在启动浏览器",selenium_ready:"等待手动登录",confirming:"正在验证登录状态",success:"登录成功",failed:"登录失败"}[this.loginStatus]||"登录中"},showProgress(){return["starting","checking","selenium_starting","confirming"].includes(this.loginStatus)},progressStatus(){return this.loginStatus==="success"?"success":this.loginStatus==="failed"?"exception":null},showCancelButton(){return["starting","checking","selenium_starting","selenium_ready"].includes(this.loginStatus)}},watch:{modelValue(t){t?this.startLogin():this.resetDialog()}},methods:{async startLogin(t=!1){this.forceRelogin=t,this.resetDialog(),this.loginStatus="starting",this.progressPercentage=10;try{const e=await this.$api.login(t);e.success?e.method==="cookies"?(this.loginStatus="success",this.statusMessage="使用现有登录信息登录成功",this.progressPercentage=100,this.$emit("login-success",e)):e.method==="selenium"&&(this.loginStatus="selenium_ready",this.statusMessage=e.message,this.progressPercentage=50):(this.loginStatus="failed",this.errorMessage=e.message||"登录启动失败",this.progressPercentage=0)}catch(e){console.error("登录失败:",e),this.loginStatus="failed",this.errorMessage=e.message||"网络连接失败",this.progressPercentage=0}},async confirmLogin(){this.confirming=!0,this.loginStatus="confirming",this.statusMessage="正在验证登录状态...",this.progressPercentage=80;try{const t=await this.$api.confirmLogin();t.success?(this.loginStatus="success",this.statusMessage=`登录成功！已保存 ${t.cookies_count||0} 个认证信息`,this.progressPercentage=100,this.$emit("login-success",t)):(this.loginStatus="failed",this.errorMessage=t.message||"登录验证失败",this.progressPercentage=50,this.$emit("login-failed",t))}catch(t){console.error("确认登录失败:",t),this.loginStatus="failed",this.errorMessage=t.message||"验证过程出错",this.progressPercentage=50,this.$emit("login-failed",{error:t.message})}finally{this.confirming=!1}},async cancelLogin(){try{await this.$api.cancelLogin(),this.$emit("login-cancelled"),this.visible=!1}catch(t){console.error("取消登录失败:",t),this.visible=!1}},retryLogin(){this.startLogin(!0)},closeDialog(){this.visible=!1},resetDialog(){this.loginStatus="starting",this.statusMessage="正在检查登录状态...",this.errorMessage="",this.confirming=!1,this.progressPercentage=0}}},gt={class:"login-dialog"},ht={class:"status-section"},mt={class:"status-icon"},_t={class:"status-text"},pt={key:1,class:"login-steps"},ft={class:"dialog-footer"};function wt(t,e,o,r,n,i){const y=c("Loading"),d=O,g=c("Monitor"),_=c("SuccessFilled"),h=c("CircleCloseFilled"),C=c("InfoFilled"),b=Z,v=ce,R=c("Check"),w=x,k=c("Close"),L=c("Refresh"),A=Pe;return m(),p(A,{modelValue:i.visible,"onUpdate:modelValue":e[0]||(e[0]=M=>i.visible=M),title:"Pixiv 登录",width:"500px","close-on-click-modal":!1,"close-on-press-escape":!1,"show-close":!1},{footer:a(()=>[l("div",ft,[n.loginStatus==="selenium_ready"?(m(),p(w,{key:0,type:"primary",onClick:i.confirmLogin,loading:n.confirming},{default:a(()=>[s(d,null,{default:a(()=>[s(R)]),_:1}),e[2]||(e[2]=f(" 确认登录 ",-1))]),_:1,__:[2]},8,["onClick","loading"])):T("",!0),i.showCancelButton?(m(),p(w,{key:1,onClick:i.cancelLogin,disabled:n.confirming},{default:a(()=>[s(d,null,{default:a(()=>[s(k)]),_:1}),e[3]||(e[3]=f(" 取消 ",-1))]),_:1,__:[3]},8,["onClick","disabled"])):T("",!0),n.loginStatus==="success"?(m(),p(w,{key:2,type:"primary",onClick:i.closeDialog},{default:a(()=>[s(d,null,{default:a(()=>[s(R)]),_:1}),e[4]||(e[4]=f(" 完成 ",-1))]),_:1,__:[4]},8,["onClick"])):T("",!0),n.loginStatus==="failed"?(m(),p(w,{key:3,type:"primary",onClick:i.retryLogin},{default:a(()=>[s(d,null,{default:a(()=>[s(L)]),_:1}),e[5]||(e[5]=f(" 重试 ",-1))]),_:1,__:[5]},8,["onClick"])):T("",!0)])]),default:a(()=>[l("div",gt,[l("div",ht,[l("div",mt,[n.loginStatus==="checking"?(m(),p(d,{key:0,class:"rotating"},{default:a(()=>[s(y)]),_:1})):n.loginStatus==="selenium_ready"?(m(),p(d,{key:1,color:"#409EFF"},{default:a(()=>[s(g)]),_:1})):n.loginStatus==="success"?(m(),p(d,{key:2,color:"#67C23A"},{default:a(()=>[s(_)]),_:1})):n.loginStatus==="failed"?(m(),p(d,{key:3,color:"#F56C6C"},{default:a(()=>[s(h)]),_:1})):(m(),p(d,{key:4,color:"#E6A23C"},{default:a(()=>[s(C)]),_:1}))]),l("div",_t,[l("h3",null,S(i.statusTitle),1),l("p",null,S(n.statusMessage),1)])]),i.showProgress?(m(),p(b,{key:0,percentage:n.progressPercentage,status:i.progressStatus,"stroke-width":8,class:"progress-bar"},null,8,["percentage","status"])):T("",!0),n.loginStatus==="selenium_ready"?(m(),P("div",pt,[s(v,{title:"请在浏览器中完成登录",type:"info",closable:!1,"show-icon":""},{default:a(()=>e[1]||(e[1]=[l("ol",{class:"steps-list"},[l("li",null,"在打开的浏览器窗口中访问 Pixiv 登录页面"),l("li",null,"输入您的用户名和密码完成登录"),l("li",null,"确保能正常访问 Pixiv 主页"),l("li",null,'返回此处点击"确认登录"按钮')],-1)])),_:1})])):T("",!0),n.errorMessage?(m(),p(v,{key:2,title:n.errorMessage,type:"error",closable:!1,"show-icon":"",class:"error-alert"},null,8,["title"])):T("",!0)])]),_:1},8,["modelValue"])}const St=I(ut,[["render",wt],["__scopeId","data-v-77e36556"]]);class yt{constructor(){this.errorTypes={NETWORK_ERROR:"NETWORK_ERROR",CONNECTION_ERROR:"CONNECTION_ERROR",TIMEOUT_ERROR:"TIMEOUT_ERROR",AUTH_ERROR:"AUTH_ERROR",LOGIN_REQUIRED:"LOGIN_REQUIRED",TOKEN_EXPIRED:"TOKEN_EXPIRED",CONFIG_ERROR:"CONFIG_ERROR",VALIDATION_ERROR:"VALIDATION_ERROR",DOWNLOAD_ERROR:"DOWNLOAD_ERROR",FILE_ERROR:"FILE_ERROR",SYSTEM_ERROR:"SYSTEM_ERROR",UNKNOWN_ERROR:"UNKNOWN_ERROR"},this.errorMessages={[this.errorTypes.NETWORK_ERROR]:"网络连接失败，请检查网络设置",[this.errorTypes.CONNECTION_ERROR]:"无法连接到服务器，请稍后重试",[this.errorTypes.TIMEOUT_ERROR]:"请求超时，请检查网络连接",[this.errorTypes.AUTH_ERROR]:"认证失败，请重新登录",[this.errorTypes.LOGIN_REQUIRED]:"请先登录Pixiv账户",[this.errorTypes.TOKEN_EXPIRED]:"登录已过期，请重新登录",[this.errorTypes.CONFIG_ERROR]:"配置错误，请检查设置",[this.errorTypes.VALIDATION_ERROR]:"输入验证失败，请检查输入内容",[this.errorTypes.DOWNLOAD_ERROR]:"下载失败，请重试",[this.errorTypes.FILE_ERROR]:"文件操作失败，请检查文件权限",[this.errorTypes.SYSTEM_ERROR]:"系统错误，请联系技术支持",[this.errorTypes.UNKNOWN_ERROR]:"未知错误，请重试"}}handleApiError(e,o=""){if(e.error_type&&e.message)return this._logError({type:e.error_type,userMessage:e.message},o),{type:e.error_type,message:e.message,originalError:e,context:o};const r=this._parseError(e);return this._logError(r,o),{type:r.type,message:r.userMessage,originalError:e,context:o}}handleError(e,o=""){if(e.error_type&&e.message)return this.handleApiError(e,o);let r=this.errorTypes.UNKNOWN_ERROR;const n=(e.message||e.toString()).toLowerCase();return n.includes("network")||n.includes("connection")?r=this.errorTypes.NETWORK_ERROR:n.includes("timeout")?r=this.errorTypes.TIMEOUT_ERROR:(n.includes("auth")||n.includes("login"))&&(r=this.errorTypes.AUTH_ERROR),{type:r,message:this.errorMessages[r],originalError:e,context:o}}handleConfigError(e,o=[]){return{type:this.errorTypes.CONFIG_ERROR,message:o.length>0?o.join("; "):this.errorMessages[this.errorTypes.CONFIG_ERROR],validationErrors:o,originalError:e}}handleDownloadError(e){let o=this.errorTypes.DOWNLOAD_ERROR;return(e.message?.includes("file")||e.message?.includes("permission"))&&(o=this.errorTypes.FILE_ERROR),{type:o,message:this.errorMessages[o],originalError:e}}_parseError(e){if(typeof e=="string")return{type:this._detectErrorType(e),userMessage:e,originalMessage:e};if(e instanceof Error){const o=this._detectErrorType(e.message);return{type:o,userMessage:this.errorMessages[o]||e.message,originalMessage:e.message}}if(e.response){const o=e.response.status;let r=this.errorTypes.UNKNOWN_ERROR;return o===401||o===403?r=this.errorTypes.AUTH_ERROR:o>=500?r=this.errorTypes.SYSTEM_ERROR:o>=400&&(r=this.errorTypes.CONFIG_ERROR),{type:r,userMessage:this.errorMessages[r],originalMessage:e.response.data?.message||e.message}}return{type:this.errorTypes.UNKNOWN_ERROR,userMessage:this.errorMessages[this.errorTypes.UNKNOWN_ERROR],originalMessage:e.toString()}}_detectErrorType(e){const o=e.toLowerCase();return o.includes("auth")||o.includes("login")||o.includes("认证")?this.errorTypes.AUTH_ERROR:o.includes("network")||o.includes("connection")||o.includes("网络")?this.errorTypes.NETWORK_ERROR:o.includes("timeout")||o.includes("超时")?this.errorTypes.TIMEOUT_ERROR:o.includes("config")||o.includes("validation")||o.includes("配置")?this.errorTypes.CONFIG_ERROR:o.includes("download")||o.includes("file")||o.includes("下载")?this.errorTypes.DOWNLOAD_ERROR:this.errorTypes.UNKNOWN_ERROR}_logError(e,o){const r=`[${e.type}] ${o?`${o}: `:""}${e.originalMessage}`;console.error(r)}createUserMessage(e,o=[]){let n=this._parseError(e).userMessage;return o.length>0&&(n+=`

建议：
`+o.map(i=>`• ${i}`).join(`
`)),n}isErrorType(e,o){return this._parseError(e).type===o}getRetryAdvice(e){switch(this._parseError(e).type){case this.errorTypes.NETWORK_ERROR:case this.errorTypes.CONNECTION_ERROR:return["检查网络连接","稍后重试","检查防火墙设置"];case this.errorTypes.TIMEOUT_ERROR:return["检查网络速度","稍后重试","减少并发请求数"];case this.errorTypes.AUTH_ERROR:return["重新登录","检查账户状态","清除浏览器缓存"];case this.errorTypes.CONFIG_ERROR:return["检查配置设置","重置为默认配置","联系技术支持"];case this.errorTypes.DOWNLOAD_ERROR:return["检查磁盘空间","检查文件权限","更换下载路径"];default:return["重试操作","重启应用","联系技术支持"]}}}const X=new yt;const vt={name:"LoginStatus",inject:["$api","$ipc"],components:{User:De,Refresh:de,Key:Ie,SuccessFilled:B,WarningFilled:H,Loading:Y,LoginDialog:St},data(){return{checking:!1,logging:!1,loginDialogVisible:!1}},computed:{...V(["isLoggedIn"]),loginStatus(){const t=this.$store.state.loginStatus;return!t||t.includes("�")?this.isLoggedIn?"已登录":"未登录":{"Checking...":"检查中...","Logged In":"已登录","Not Logged In":"未登录","Connection Failed":"连接失败","Login Success":"登录成功","Login Failed":"登录失败"}[t]||(this.isLoggedIn?"已登录":"未登录")},statusType(){return this.loginStatus.includes("已登录")?"success":this.loginStatus.includes("失败")?"danger":"warning"},statusIcon(){return this.loginStatus.includes("已登录")?B:this.loginStatus.includes("失败")?H:Y},statusClass(){return this.loginStatus.includes("已登录")?"success":this.loginStatus.includes("失败")?"danger":this.loginStatus.includes("检查中")?"info":"warning"}},methods:{async checkLogin(){this.checking=!0;try{const t=await this.$api.getAuthStatus();this.$store.commit("setLoginStatus",{isLoggedIn:t.authenticated||!1,status:t.message||(t.authenticated?"已登录":"未登录")})}catch(t){console.error("检查登录状态失败:",t),this.$store.commit("setLoginStatus",{isLoggedIn:!1,status:"连接失败"}),this.$message.error("检查登录状态失败")}finally{this.checking=!1}},showLoginDialog(){this.loginDialogVisible=!0},onLoginSuccess(t){console.log("登录成功:",t),this.$store.commit("setLoginStatus",{isLoggedIn:!0,status:"登录成功"}),this.$message.success("登录成功！"),this.loginDialogVisible=!1,this.checkLogin()},onLoginFailed(t){console.error("登录失败:",t),this.$store.commit("setLoginStatus",{isLoggedIn:!1,status:"登录失败"}),this.$message.error("登录失败: "+(t.message||t.error||"未知错误"))},onLoginCancelled(){console.log("用户取消登录"),this.$message.info("登录已取消")}},mounted(){this.checkLogin()}},kt={class:"card-header"},Rt={class:"header-left"},Ct={class:"login-content"},bt={class:"status-display"},Tt={class:"login-actions modern-button-group"};function Et(t,e,o,r,n,i){const y=c("User"),d=O,g=c("Refresh"),_=x,h=c("Key"),C=c("LoginDialog"),b=N;return m(),p(b,{class:"login-card modern-card",shadow:"hover"},{header:a(()=>[l("div",kt,[l("div",Rt,[s(d,{class:"header-icon"},{default:a(()=>[s(y)]),_:1}),e[1]||(e[1]=l("span",{class:"header-title"},"登录状态",-1))])])]),default:a(()=>[l("div",Ct,[l("div",bt,[l("div",{class:K(["status-indicator",i.statusClass])},[s(d,null,{default:a(()=>[(m(),p(ae(i.statusIcon)))]),_:1}),f(" "+S(i.loginStatus),1)],2)]),l("div",Tt,[s(_,{type:"primary",onClick:i.checkLogin,loading:n.checking,size:"small"},{default:a(()=>[s(d,null,{default:a(()=>[s(g)]),_:1}),e[2]||(e[2]=f(" 检查登录 ",-1))]),_:1,__:[2]},8,["onClick","loading"]),s(_,{type:"warning",onClick:i.showLoginDialog,loading:n.logging,size:"small"},{default:a(()=>[s(d,null,{default:a(()=>[s(h)]),_:1}),f(" "+S(t.isLoggedIn?"重新登录":"立即登录"),1)]),_:1},8,["onClick","loading"])])]),s(C,{modelValue:n.loginDialogVisible,"onUpdate:modelValue":e[0]||(e[0]=v=>n.loginDialogVisible=v),onLoginSuccess:i.onLoginSuccess,onLoginFailed:i.onLoginFailed,onLoginCancelled:i.onLoginCancelled},null,8,["modelValue","onLoginSuccess","onLoginFailed","onLoginCancelled"])]),_:1})}const Pt=I(vt,[["render",Et],["__scopeId","data-v-bc1b8302"]]);class Dt{constructor(){this.errorMessages={REQUIRED_FIELD:"此字段为必填项",INVALID_NUMBER:"请输入有效的数字",INVALID_RANGE:"数值超出有效范围",INVALID_PATH:"请选择有效的下载路径",PATH_NOT_WRITABLE:"下载路径无写入权限",INVALID_PAGE_RANGE:"起始页码不能大于结束页码",PAGE_TOO_SMALL:"页码必须大于0",PAGE_TOO_LARGE:"页码不能超过1000",SEARCH_KEYWORD_REQUIRED:"搜索模式下请输入搜索关键词",KEYWORD_TOO_SHORT:"搜索关键词至少需要1个字符",KEYWORD_TOO_LONG:"搜索关键词不能超过100个字符",ARTIST_ID_REQUIRED:"画师模式下请输入画师ID",INVALID_ARTIST_ID:"请输入有效的画师ID（正整数）",LOGIN_REQUIRED:"请先登录Pixiv账号",AUTH_EXPIRED:"登录已过期，请重新登录",INVALID_BOOKMARK_COUNT:"收藏数必须为正整数",BOOKMARK_RANGE_ERROR:"最小收藏数不能大于最大收藏数"}}validateDownloadConfig(e){const o=[];switch(e.save_path||o.push(this.errorMessages.INVALID_PATH),e.download_mode){case"search":o.push(...this._validateSearchConfig(e));break;case"user":o.push(...this._validateUserConfig(e));break;case"date":o.push(...this._validateDateConfig(e));break;case"ranking":o.push(...this._validateRankingConfig(e));break}return o}_validateSearchConfig(e){const o=[];return e.search_keyword?(e.search_keyword.length<1&&o.push(this.errorMessages.KEYWORD_TOO_SHORT),e.search_keyword.length>100&&o.push(this.errorMessages.KEYWORD_TOO_LONG)):o.push(this.errorMessages.SEARCH_KEYWORD_REQUIRED),o.push(...this._validatePageRange(e.start_page,e.end_page)),e.min_bookmarks<0&&o.push(this.errorMessages.INVALID_BOOKMARK_COUNT),o}_validateUserConfig(e){const o=[];return!e.user_id||e.user_id<=0?o.push(this.errorMessages.ARTIST_ID_REQUIRED):Number.isInteger(e.user_id)||o.push(this.errorMessages.INVALID_ARTIST_ID),o.push(...this._validatePageRange(e.start_page,e.end_page)),o}_validateDateConfig(e){const o=[];return e.date_mode==="by_page_range"?o.push(...this._validatePageRange(e.start_page,e.end_page)):e.date_mode==="by_date_range"&&e.days<=0&&o.push("天数必须大于0"),o}_validateRankingConfig(e){return[]}_validatePageRange(e,o){const r=[];return e<1&&r.push(this.errorMessages.PAGE_TOO_SMALL),o<1&&r.push(this.errorMessages.PAGE_TOO_SMALL),e>o&&r.push(this.errorMessages.INVALID_PAGE_RANGE),(e>1e3||o>1e3)&&r.push(this.errorMessages.PAGE_TOO_LARGE),r}validateAuthStatus(e){return e?[]:[this.errorMessages.LOGIN_REQUIRED]}validatePath(e){const o=[];return(!e||e.trim()==="")&&o.push(this.errorMessages.INVALID_PATH),e&&!/^[a-zA-Z]:[\\\/]/.test(e)&&!/^\//.test(e)&&o.push("请输入有效的文件路径"),o}validateNumberRange(e,o,r,n="数值"){const i=[];return typeof e!="number"||isNaN(e)?(i.push(`${n}必须是有效数字`),i):(e<o&&i.push(`${n}不能小于${o}`),e>r&&i.push(`${n}不能大于${r}`),i)}getErrorMessage(e){return this.errorMessages[e]||"未知错误"}formatErrors(e){return!e||e.length===0?"":e.length===1?e[0]:e.map((o,r)=>`${r+1}. ${o}`).join(`
`)}hasErrors(e){return e&&e.length>0}}const It=new Dt;class Lt{constructor(){this.defaultConfig={save_path:"",skip_existing:!0,create_info_file:!0,download_limit:0,download_mode:"date",classify_mode:"by_date",start_page:1,end_page:5,days:7,date_mode:"by_page_range",search_keyword:"",search_save_path:"",min_bookmarks:0,search_mode:"all",search_config:{category:"综合",bookmark_count:-1,content_mode:"全部"},ranking_category:"overall",user_id:0,user_save_path:""}}createUnifiedConfig(e,o){const r={...this.defaultConfig},n=this._getDownloadPath(e,o);switch(r.save_path=n,o.classifyMode&&(r.classify_mode=o.classifyMode),e){case"following":this._configureFollowing(r,o);break;case"search":this._configureSearch(r,o,n);break;case"ranking":this._configureRanking(r,o);break;case"artist":this._configureArtist(r,o,n);break;default:throw new Error(`未知的下载模式: ${e}`)}return r}_configureFollowing(e,o){e.download_mode="date",e.start_page=o?.pageStart||1,e.end_page=o?.pageEnd||5,e.days=o?.days||7,e.date_mode=o?.downloadType==="days"?"by_date_range":"by_page_range"}_configureSearch(e,o,r){e.download_mode="search",e.search_keyword=o?.keyword||"",e.search_save_path=r,e.start_page=o?.pageStart||1,e.end_page=o?.pageEnd||5,e.search_mode=o?.searchMode||"all",o?.enableBookmarkFilter&&o?.bookmarkCount?(e.min_bookmarks=o.bookmarkCount,e.search_config={category:this._mapSearchTypeToCategory(o.searchType),bookmark_count:o.bookmarkCount,content_mode:this._mapSearchModeToContentMode(o.searchMode)}):(e.min_bookmarks=0,e.search_config={category:this._mapSearchTypeToCategory(o?.searchType),bookmark_count:-1,content_mode:this._mapSearchModeToContentMode(o?.searchMode)})}_configureRanking(e,o){e.download_mode="ranking",e.ranking_category=o?.rankingType||"overall",e.start_page=1,e.end_page=1}_configureArtist(e,o,r){e.download_mode="user",e.user_id=o?.artistId?Number(o.artistId):0,e.user_save_path=r,e.start_page=o?.pageStart||1,e.end_page=o?.pageEnd||5}_getDownloadPath(e,o){switch(e){case"following":return o?.downloadPath||"";case"search":return o?.downloadPath||"";case"ranking":return o?.downloadPath||"";case"artist":return o?.downloadPath||"";default:return""}}_mapSearchTypeToCategory(e){return{artworks:"综合",illustrations:"插画",manga:"漫画"}[e]||"综合"}_mapSearchModeToContentMode(e){return{all:"全部",safe:"全年龄",r18:"R18"}[e]||"全部"}validateConfig(e){return It.validateDownloadConfig(e)}generatePreviewUrl(e,o){switch(e){case"search":const r=o?.keyword||"",n=o?.searchType||"artworks";if(o?.enableBookmarkFilter&&o?.bookmarkCount){const _=`${r}${o.bookmarkCount}users入り`;return`https://www.pixiv.net/tags/${encodeURIComponent(_)}/${n}`}else return`https://www.pixiv.net/tags/${encodeURIComponent(r)}/${n}`;case"following":return"https://www.pixiv.net/bookmark_new_illust.php";case"ranking":return`https://www.pixiv.net/ranking.php?mode=${o?.rankingType||"overall"}`;case"artist":const y=o?.artistId||"";if(!y)return"";const d=o?.pageStart||1,g=`https://www.pixiv.net/users/${y}/artworks`;return d>1?`${g}?p=${d}`:g;default:return""}}generateArtistUrl(e,o=1){if(!e)throw new Error("画师作品模式下必须提供画师ID");const r=`https://www.pixiv.net/users/${e}/artworks`;return o>1?`${r}?p=${o}`:r}}const W=new Lt;const Ot={name:"DownloadSettings",inject:["$api","$ipc"],components:{Download:ee,VideoPlay:ue,VideoPause:ge,View:Le},data(){return{downloadMode:"following",classifyMode:"by_date",isStarting:!1,isStopping:!1,isPaused:!1,followingSettings:{downloadType:"pages",days:7,pageStart:1,pageEnd:5,downloadPath:""},searchSettings:{keyword:"",searchType:"artworks",enableBookmarkFilter:!1,bookmarkCount:1e3,searchMode:"all",pageStart:1,pageEnd:5,downloadPath:""},rankingSettings:{rankingType:"overall",rankingPeriod:"daily",rankingMode:"safe",rankingDate:(()=>{const t=new Date;return t.getFullYear()+"-"+String(t.getMonth()+1).padStart(2,"0")+"-"+String(t.getDate()).padStart(2,"0")})(),downloadPath:""},artistSettings:{artistId:"",pageStart:1,pageEnd:5,downloadPath:""}}},computed:{...V(["isDownloading"]),currentSettings(){switch(this.downloadMode){case"following":return this.followingSettings;case"search":return this.searchSettings;case"ranking":return this.rankingSettings;case"artist":return this.artistSettings;default:return{}}},canStartDownload(){if(!this.getCurrentDownloadPath())return!1;switch(this.downloadMode){case"following":return!0;case"search":return this.searchSettings.keyword;case"ranking":return!0;case"artist":return this.artistSettings.artistId;default:return!1}},canStart(){return this.canStartDownload&&!this.isStarting&&!this.isDownloading}},methods:{onModeChange(){this.saveSettings()},onRankingTypeChange(){this.rankingSettings.rankingType!=="overall"&&this.rankingSettings.rankingPeriod==="daily_ai"&&(this.rankingSettings.rankingPeriod="daily"),this.saveSettings()},async selectPath(t){try{const e=await window.electronAPI.showOpenDialog({properties:["openDirectory"],title:"选择下载路径"});if(!e.canceled&&e.filePaths.length>0){const o=e.filePaths[0];switch(t){case"following":this.followingSettings.downloadPath=o;break;case"search":this.searchSettings.downloadPath=o;break;case"ranking":this.rankingSettings.downloadPath=o;break;case"artist":this.artistSettings.downloadPath=o;break}this.saveSettings()}}catch{this.$message.error("选择路径失败")}},saveSettings(){try{const t={mode:this.downloadMode,classifyMode:this.classifyMode,following:JSON.parse(JSON.stringify(this.followingSettings)),search:JSON.parse(JSON.stringify(this.searchSettings)),ranking:JSON.parse(JSON.stringify(this.rankingSettings)),artist:JSON.parse(JSON.stringify(this.artistSettings))};console.log("💾 保存下载设置:",t),this.$store.commit("updateDownloadSettings",t),this.$store.dispatch("saveSettings").then(()=>{console.log("✅ 设置已保存到存储")}).catch(e=>{console.error("❌ 保存设置失败:",e)})}catch(t){console.error("❌ 准备保存设置时出错:",t)}},generateUrl(){const t=this.getCurrentSettings();return W.generatePreviewUrl(this.downloadMode,t)},previewUrl(){const t=this.getCurrentSettings(),e=W.generatePreviewUrl(this.downloadMode,t);e?(this.$message.info(`生成的URL: ${e}`),console.log("Generated URL:",e)):this.$message.warning("请先完成设置")},getCurrentDownloadPath(){switch(this.downloadMode||"following"){case"following":return this.followingSettings?.downloadPath;case"search":return this.searchSettings?.downloadPath;case"ranking":return this.rankingSettings?.downloadPath;case"artist":return this.artistSettings?.downloadPath;default:return null}},convertToBackendConfig(t,e){return W.createUnifiedConfig(t,e)},async startDownload(){if(!this.canStartDownload){this.$message.warning("请完成必要的设置");return}this.isStarting=!0;try{const t=this.downloadMode||"following",e=this.getCurrentSettings(),o=this.convertToBackendConfig(t,e);console.log("🚀 准备开始下载，配置:",o);try{JSON.stringify(o),console.log("✅ 配置序列化验证通过")}catch(n){throw console.error("❌ 配置序列化失败:",n),new Error("配置包含不可序列化的对象: "+n.message)}console.log("📡 调用API开始下载...");const r=await this.$api.startDownload(o);if(r.success)this.$store.commit("setDownloading",!0),this.$store.commit("addLog","🚀 下载任务已提交到后台"),this.$message.success("下载任务已在后台启动，请查看日志获取进度");else throw new Error(r.message||"启动下载失败")}catch(t){console.error("开始下载失败:",t),this.$store.commit("addLog",`❌ 下载启动失败: ${t.message}`),this.$message.error("开始下载失败: "+t.message)}finally{this.isStarting=!1}},async stopDownload(){this.isStopping=!0;try{const t=await this.$api.stopDownload();if(t.success)this.$store.commit("setDownloading",!1),this.isPaused=!1,this.$store.commit("addLog","⏹️ 下载任务已停止"),this.$message.success("下载任务已停止");else throw new Error(t.message||"停止下载失败")}catch(t){console.error("停止下载失败:",t),this.$message.error("停止下载失败: "+t.message)}finally{this.isStopping=!1}},async pauseDownload(){try{if(this.isPaused){const t=await window.electronAPI.resumeDownload();t.success?(this.isPaused=!1,this.$store.commit("addLog","▶️ 下载任务已从断点继续"),this.$message.success("下载已从断点继续")):(this.$message.warning(t.message||"未找到有效断点，请重新开始下载"),this.$store.commit("addLog","⚠️ "+(t.message||"未找到有效断点")))}else{const t=await window.electronAPI.pauseDownload();if(t.success)this.isPaused=!0,this.$store.commit("addLog","⏸️ 下载任务已暂停，断点已保存"),this.$message.success("下载已暂停，断点已保存");else throw new Error(t.message||"暂停失败")}}catch(t){const e=this.isPaused?"继续下载失败":"暂停下载失败";this.$message.error(`${e}: ${t.message}`),this.$store.commit("addLog",`❌ ${e}: ${t.message}`)}},getCurrentSettings(){let t={};switch(this.downloadMode){case"following":t=this.followingSettings;break;case"search":t=this.searchSettings;break;case"ranking":t=this.rankingSettings;break;case"artist":t=this.artistSettings;break;default:t={}}return{...t,classifyMode:this.classifyMode}},loadSettings(){const t=this.$store.state.downloadSettings;console.log("📥 加载下载设置:",t),t&&(this.downloadMode=t.mode||"following",this.classifyMode=t.classifyMode||"by_date",t.following&&(Object.assign(this.followingSettings,t.following),console.log("📥 加载关注设置:",t.following)),t.search&&(Object.assign(this.searchSettings,t.search),console.log("📥 加载搜索设置:",t.search)),t.ranking&&(Object.assign(this.rankingSettings,t.ranking),console.log("📥 加载排行榜设置:",t.ranking)),t.artist&&(Object.assign(this.artistSettings,t.artist),console.log("📥 加载画师设置:",t.artist)))}},mounted(){this.loadSettings()},watch:{downloadMode(){this.saveSettings()},"$store.state.downloadSettings":{handler(){this.loadSettings()},deep:!0}}},At={class:"card-header"},Mt={class:"header-left"},Vt={class:"range-input"},xt={class:"path-input"},Nt={class:"bookmark-filter"},Ut={class:"range-input"},$t={class:"path-input"},zt={class:"path-input"},Ft={class:"range-input"},Wt={class:"path-input"},Bt={class:"action-buttons"};function Gt(t,e,o,r,n,i){const y=c("Download"),d=O,g=he,_=me,h=_e,C=Oe,b=Ae,v=pe,R=fe,w=x,k=te,L=Me,A=se,M=we,$=c("VideoPlay"),z=c("VideoPause"),E=c("View"),Q=Se,j=N;return m(),p(j,{class:"download-settings-card modern-card",shadow:"hover"},{header:a(()=>[l("div",At,[l("div",Mt,[s(d,{class:"header-icon"},{default:a(()=>[s(y)]),_:1}),e[28]||(e[28]=l("span",{class:"header-title"},"下载设置",-1))])])]),default:a(()=>[s(Q,{model:i.currentSettings,"label-width":"80px",size:"small",class:"modern-form"},{default:a(()=>[s(h,{label:"下载模式"},{default:a(()=>[s(_,{modelValue:n.downloadMode,"onUpdate:modelValue":e[0]||(e[0]=u=>n.downloadMode=u),onChange:i.onModeChange,style:{width:"100%"}},{default:a(()=>[s(g,{label:"关注画师作品",value:"following"}),s(g,{label:"搜索作品",value:"search"}),s(g,{label:"排行榜作品",value:"ranking"}),s(g,{label:"画师作品",value:"artist"})]),_:1},8,["modelValue","onChange"])]),_:1}),s(h,{label:"文件分类"},{default:a(()=>[s(_,{modelValue:n.classifyMode,"onUpdate:modelValue":e[1]||(e[1]=u=>n.classifyMode=u),onChange:i.saveSettings,style:{width:"100%"}},{default:a(()=>[s(g,{label:"按日期分类",value:"by_date"}),s(g,{label:"按作者分类",value:"by_author"}),s(g,{label:"按类型分类",value:"by_type"}),s(g,{label:"平铺结构",value:"flat"})]),_:1},8,["modelValue","onChange"])]),_:1}),n.downloadMode==="following"?(m(),P(F,{key:0},[s(h,{label:"下载方式"},{default:a(()=>[s(b,{modelValue:n.followingSettings.downloadType,"onUpdate:modelValue":e[2]||(e[2]=u=>n.followingSettings.downloadType=u),onChange:i.saveSettings},{default:a(()=>[s(C,{label:"pages"},{default:a(()=>e[29]||(e[29]=[f("按页码",-1)])),_:1,__:[29]}),s(C,{label:"days"},{default:a(()=>e[30]||(e[30]=[f("按天数",-1)])),_:1,__:[30]})]),_:1},8,["modelValue","onChange"])]),_:1}),n.followingSettings.downloadType==="days"?(m(),p(h,{key:0,label:"获取天数"},{default:a(()=>[s(v,{modelValue:n.followingSettings.days,"onUpdate:modelValue":e[3]||(e[3]=u=>n.followingSettings.days=u),min:1,max:365,style:{width:"100%"},onChange:i.saveSettings},null,8,["modelValue","onChange"])]),_:1})):T("",!0),n.followingSettings.downloadType==="pages"?(m(),p(h,{key:1,label:"页码范围"},{default:a(()=>[l("div",Vt,[s(v,{modelValue:n.followingSettings.pageStart,"onUpdate:modelValue":e[4]||(e[4]=u=>n.followingSettings.pageStart=u),min:1,max:n.followingSettings.pageEnd,size:"small"},null,8,["modelValue","max"]),e[31]||(e[31]=l("span",{class:"range-separator"},"至",-1)),s(v,{modelValue:n.followingSettings.pageEnd,"onUpdate:modelValue":e[5]||(e[5]=u=>n.followingSettings.pageEnd=u),min:n.followingSettings.pageStart,max:1e3,size:"small"},null,8,["modelValue","min"])])]),_:1})):T("",!0),s(h,{label:"下载路径"},{default:a(()=>[l("div",xt,[s(R,{modelValue:n.followingSettings.downloadPath,"onUpdate:modelValue":e[6]||(e[6]=u=>n.followingSettings.downloadPath=u),placeholder:"H:\\Pixiv",readonly:""},null,8,["modelValue"]),s(w,{onClick:e[7]||(e[7]=u=>i.selectPath("following")),size:"small"},{default:a(()=>e[32]||(e[32]=[f(" 选择路径 ",-1)])),_:1,__:[32]})])]),_:1})],64)):T("",!0),n.downloadMode==="search"?(m(),P(F,{key:1},[s(h,{label:"搜索关键词"},{default:a(()=>[s(R,{modelValue:n.searchSettings.keyword,"onUpdate:modelValue":e[8]||(e[8]=u=>n.searchSettings.keyword=u),placeholder:"请输入搜索关键词",clearable:"",onChange:i.saveSettings},null,8,["modelValue","onChange"])]),_:1}),s(h,{label:"搜索范围"},{default:a(()=>[s(_,{modelValue:n.searchSettings.searchType,"onUpdate:modelValue":e[9]||(e[9]=u=>n.searchSettings.searchType=u),onChange:i.saveSettings,style:{width:"100%"}},{default:a(()=>[s(g,{label:"插画·漫画",value:"artworks"}),s(g,{label:"插画",value:"illustrations"}),s(g,{label:"漫画",value:"manga"})]),_:1},8,["modelValue","onChange"])]),_:1}),s(h,{label:"收藏过滤"},{default:a(()=>[l("div",Nt,[s(k,{modelValue:n.searchSettings.enableBookmarkFilter,"onUpdate:modelValue":e[10]||(e[10]=u=>n.searchSettings.enableBookmarkFilter=u),"active-text":"启用收藏过滤","inactive-text":"不启用",onChange:i.saveSettings},null,8,["modelValue","onChange"]),n.searchSettings.enableBookmarkFilter?(m(),p(_,{key:0,modelValue:n.searchSettings.bookmarkCount,"onUpdate:modelValue":e[11]||(e[11]=u=>n.searchSettings.bookmarkCount=u),onChange:i.saveSettings,style:{width:"150px","margin-left":"10px"}},{default:a(()=>[s(g,{label:"1000users入り",value:1e3}),s(g,{label:"2000users入り",value:2e3}),s(g,{label:"5000users入り",value:5e3}),s(g,{label:"10000users入り",value:1e4}),s(g,{label:"20000users入り",value:2e4})]),_:1},8,["modelValue","onChange"])):T("",!0)])]),_:1}),s(h,{label:"搜索模式"},{default:a(()=>[s(_,{modelValue:n.searchSettings.searchMode,"onUpdate:modelValue":e[12]||(e[12]=u=>n.searchSettings.searchMode=u),onChange:i.saveSettings,style:{width:"100%"}},{default:a(()=>[s(g,{label:"全部",value:"all"}),s(g,{label:"全年龄",value:"safe"}),s(g,{label:"R-18",value:"r18"})]),_:1},8,["modelValue","onChange"])]),_:1}),s(h,{label:"页码范围"},{default:a(()=>[l("div",Ut,[s(v,{modelValue:n.searchSettings.pageStart,"onUpdate:modelValue":e[13]||(e[13]=u=>n.searchSettings.pageStart=u),min:1,max:n.searchSettings.pageEnd,size:"small"},null,8,["modelValue","max"]),e[33]||(e[33]=l("span",{class:"range-separator"},"至",-1)),s(v,{modelValue:n.searchSettings.pageEnd,"onUpdate:modelValue":e[14]||(e[14]=u=>n.searchSettings.pageEnd=u),min:n.searchSettings.pageStart,max:1e3,size:"small"},null,8,["modelValue","min"])])]),_:1}),s(h,{label:"下载路径"},{default:a(()=>[l("div",$t,[s(R,{modelValue:n.searchSettings.downloadPath,"onUpdate:modelValue":e[15]||(e[15]=u=>n.searchSettings.downloadPath=u),placeholder:"H:\\Pixiv",readonly:""},null,8,["modelValue"]),s(w,{onClick:e[16]||(e[16]=u=>i.selectPath("search")),size:"small"},{default:a(()=>e[34]||(e[34]=[f(" 选择路径 ",-1)])),_:1,__:[34]})])]),_:1})],64)):T("",!0),n.downloadMode==="ranking"?(m(),P(F,{key:2},[s(h,{label:"排行榜类型"},{default:a(()=>[s(_,{modelValue:n.rankingSettings.rankingType,"onUpdate:modelValue":e[17]||(e[17]=u=>n.rankingSettings.rankingType=u),onChange:i.onRankingTypeChange,style:{width:"100%"}},{default:a(()=>[s(g,{label:"综合",value:"overall"}),s(g,{label:"插画",value:"illust"}),s(g,{label:"动图",value:"ugoira"}),s(g,{label:"漫画",value:"manga"})]),_:1},8,["modelValue","onChange"])]),_:1}),s(h,{label:"排行榜选择"},{default:a(()=>[s(_,{modelValue:n.rankingSettings.rankingPeriod,"onUpdate:modelValue":e[18]||(e[18]=u=>n.rankingSettings.rankingPeriod=u),onChange:i.saveSettings,style:{width:"100%"}},{default:a(()=>[s(g,{label:"今日",value:"daily"}),s(g,{label:"本周",value:"weekly"}),n.rankingSettings.rankingType==="overall"?(m(),p(g,{key:0,label:"AI生成",value:"daily_ai"})):T("",!0)]),_:1},8,["modelValue","onChange"])]),_:1}),s(h,{label:"排行榜模式"},{default:a(()=>[s(_,{modelValue:n.rankingSettings.rankingMode,"onUpdate:modelValue":e[19]||(e[19]=u=>n.rankingSettings.rankingMode=u),onChange:i.saveSettings,style:{width:"100%"}},{default:a(()=>[s(g,{label:"全年龄",value:"safe"}),s(g,{label:"R-18",value:"r18"})]),_:1},8,["modelValue","onChange"])]),_:1}),s(h,{label:"排行榜日期"},{default:a(()=>[s(L,{modelValue:n.rankingSettings.rankingDate,"onUpdate:modelValue":e[20]||(e[20]=u=>n.rankingSettings.rankingDate=u),type:"date",placeholder:"选择日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"},onChange:i.saveSettings},null,8,["modelValue","onChange"])]),_:1}),s(h,{label:"下载路径"},{default:a(()=>[l("div",zt,[s(R,{modelValue:n.rankingSettings.downloadPath,"onUpdate:modelValue":e[21]||(e[21]=u=>n.rankingSettings.downloadPath=u),placeholder:"H:\\Pixiv",readonly:""},null,8,["modelValue"]),s(w,{onClick:e[22]||(e[22]=u=>i.selectPath("ranking")),size:"small"},{default:a(()=>e[35]||(e[35]=[f(" 选择路径 ",-1)])),_:1,__:[35]})])]),_:1})],64)):T("",!0),n.downloadMode==="artist"?(m(),P(F,{key:3},[s(h,{label:"画师ID"},{default:a(()=>[s(R,{modelValue:n.artistSettings.artistId,"onUpdate:modelValue":e[23]||(e[23]=u=>n.artistSettings.artistId=u),placeholder:"请输入画师ID",clearable:"",onChange:i.saveSettings},null,8,["modelValue","onChange"])]),_:1}),s(h,{label:"页码范围"},{default:a(()=>[l("div",Ft,[s(v,{modelValue:n.artistSettings.pageStart,"onUpdate:modelValue":e[24]||(e[24]=u=>n.artistSettings.pageStart=u),min:1,max:n.artistSettings.pageEnd,size:"small"},null,8,["modelValue","max"]),e[36]||(e[36]=l("span",{class:"range-separator"},"至",-1)),s(v,{modelValue:n.artistSettings.pageEnd,"onUpdate:modelValue":e[25]||(e[25]=u=>n.artistSettings.pageEnd=u),min:n.artistSettings.pageStart,max:1e3,size:"small"},null,8,["modelValue","min"])])]),_:1}),s(h,{label:"下载路径"},{default:a(()=>[l("div",Wt,[s(R,{modelValue:n.artistSettings.downloadPath,"onUpdate:modelValue":e[26]||(e[26]=u=>n.artistSettings.downloadPath=u),placeholder:"H:\\Pixiv",readonly:""},null,8,["modelValue"]),s(w,{onClick:e[27]||(e[27]=u=>i.selectPath("artist")),size:"small"},{default:a(()=>e[37]||(e[37]=[f(" 选择路径 ",-1)])),_:1,__:[37]})])]),_:1})],64)):T("",!0),s(M,{"content-position":"center"},{default:a(()=>[s(A,{size:"small"},{default:a(()=>e[38]||(e[38]=[f("操作",-1)])),_:1,__:[38]})]),_:1}),l("div",Bt,[s(w,{type:"primary",size:"large",onClick:i.startDownload,disabled:!i.canStart,loading:n.isStarting,class:"control-btn start-btn"},{default:a(()=>[s(d,null,{default:a(()=>[s($)]),_:1}),e[39]||(e[39]=l("span",null,"开始下载",-1))]),_:1,__:[39]},8,["onClick","disabled","loading"]),s(w,{type:"danger",size:"large",onClick:i.stopDownload,disabled:!t.isDownloading,loading:n.isStopping,class:"control-btn stop-btn"},{default:a(()=>[s(d,null,{default:a(()=>[s(z)]),_:1}),e[40]||(e[40]=l("span",null,"停止下载",-1))]),_:1,__:[40]},8,["onClick","disabled","loading"]),s(w,{type:"warning",size:"large",onClick:i.pauseDownload,disabled:!t.isDownloading||n.isPaused,class:"control-btn pause-btn"},{default:a(()=>[s(d,null,{default:a(()=>[n.isPaused?(m(),p($,{key:1})):(m(),p(z,{key:0}))]),_:1}),l("span",null,S(n.isPaused?"继续":"暂停"),1)]),_:1},8,["onClick","disabled"]),s(w,{onClick:i.previewUrl,type:"info",size:"large"},{default:a(()=>[s(d,null,{default:a(()=>[s(E)]),_:1}),e[41]||(e[41]=l("span",null,"预览URL",-1))]),_:1,__:[41]},8,["onClick"])])]),_:1},8,["model"])]),_:1})}const Kt=I(Ot,[["render",Gt],["__scopeId","data-v-9c983967"]]);const Ht={name:"PerformanceSettings",components:{Cpu:Ve},data(){return{settings:{maxConcurrent:3,delay:1e3,retryCount:3,timeout:30,skipExisting:!0,saveOriginal:!0,createSubfolder:!0}}},computed:{...V(["downloadSettings"])},methods:{formatDelay(t){return t<1e3?`${t}毫秒`:`${(t/1e3).toFixed(1)}秒`},updateSettings(){this.$store.commit("updateDownloadSettings",this.settings),this.$store.dispatch("saveSettings")},loadSettings(){this.downloadSettings&&(this.settings={maxConcurrent:this.downloadSettings.maxConcurrent||3,delay:this.downloadSettings.delay||1e3,retryCount:this.downloadSettings.retryCount||3,timeout:this.downloadSettings.timeout||30,skipExisting:this.downloadSettings.skipExisting!==!1,saveOriginal:this.downloadSettings.saveOriginal!==!1,createSubfolder:this.downloadSettings.createSubfolder!==!1})}},mounted(){this.loadSettings()},watch:{downloadSettings:{handler(){this.loadSettings()},deep:!0}}},Yt={class:"card-header"},Jt={class:"header-left"},Qt={class:"slider-container"},jt={class:"slider-tip"},Xt={class:"slider-container"},qt={class:"slider-tip"},Zt={class:"timeout-input"};function es(t,e,o,r,n,i){const y=c("Cpu"),d=O,g=xe,_=se,h=_e,C=pe,b=we,v=te,R=Se,w=N;return m(),p(w,{class:"performance-card modern-card",shadow:"hover"},{header:a(()=>[l("div",Yt,[l("div",Jt,[s(d,{class:"header-icon"},{default:a(()=>[s(y)]),_:1}),e[7]||(e[7]=l("span",{class:"header-title"},"性能设置",-1))])])]),default:a(()=>[s(R,{model:n.settings,"label-width":"80px",size:"small",class:"modern-form"},{default:a(()=>[s(h,{label:"并发数"},{default:a(()=>[l("div",Qt,[s(g,{modelValue:n.settings.maxConcurrent,"onUpdate:modelValue":e[0]||(e[0]=k=>n.settings.maxConcurrent=k),min:1,max:10,step:1,"show-stops":"","show-input":"","show-input-controls":!1,onChange:i.updateSettings},null,8,["modelValue","onChange"]),l("div",jt,[s(_,{size:"small",type:"info"},{default:a(()=>e[8]||(e[8]=[f(" 建议值: 3-5，过高可能被限制 ",-1)])),_:1,__:[8]})])])]),_:1}),s(h,{label:"请求延迟"},{default:a(()=>[l("div",Xt,[s(g,{modelValue:n.settings.delay,"onUpdate:modelValue":e[1]||(e[1]=k=>n.settings.delay=k),min:500,max:5e3,step:100,"show-input":"","show-input-controls":!1,"format-tooltip":i.formatDelay,onChange:i.updateSettings},null,8,["modelValue","format-tooltip","onChange"]),l("div",qt,[s(_,{size:"small",type:"info"},{default:a(()=>[f(S(i.formatDelay(n.settings.delay))+"，避免请求过于频繁 ",1)]),_:1})])])]),_:1}),s(h,{label:"重试次数"},{default:a(()=>[s(C,{modelValue:n.settings.retryCount,"onUpdate:modelValue":e[2]||(e[2]=k=>n.settings.retryCount=k),min:0,max:10,style:{width:"100%"},onChange:i.updateSettings},null,8,["modelValue","onChange"])]),_:1}),s(h,{label:"超时时间"},{default:a(()=>[l("div",Zt,[s(C,{modelValue:n.settings.timeout,"onUpdate:modelValue":e[3]||(e[3]=k=>n.settings.timeout=k),min:5,max:120,style:{width:"100%"},onChange:i.updateSettings},null,8,["modelValue","onChange"]),e[9]||(e[9]=l("span",{class:"unit"},"秒",-1))])]),_:1}),s(b,{"content-position":"left"},{default:a(()=>[s(_,{size:"small"},{default:a(()=>e[10]||(e[10]=[f("高级选项",-1)])),_:1,__:[10]})]),_:1}),s(h,{label:"跳过已存在"},{default:a(()=>[s(v,{modelValue:n.settings.skipExisting,"onUpdate:modelValue":e[4]||(e[4]=k=>n.settings.skipExisting=k),"active-text":"是","inactive-text":"否",onChange:i.updateSettings},null,8,["modelValue","onChange"])]),_:1}),s(h,{label:"保存原图"},{default:a(()=>[s(v,{modelValue:n.settings.saveOriginal,"onUpdate:modelValue":e[5]||(e[5]=k=>n.settings.saveOriginal=k),"active-text":"是","inactive-text":"否",onChange:i.updateSettings},null,8,["modelValue","onChange"])]),_:1}),s(h,{label:"创建子文件夹"},{default:a(()=>[s(v,{modelValue:n.settings.createSubfolder,"onUpdate:modelValue":e[6]||(e[6]=k=>n.settings.createSubfolder=k),"active-text":"是","inactive-text":"否",onChange:i.updateSettings},null,8,["modelValue","onChange"])]),_:1})]),_:1},8,["model"])]),_:1})}const ts=I(Ht,[["render",es],["__scopeId","data-v-7a2b85ca"]]);const ss={name:"ControlButtons",inject:["$api","$ipc"],components:{Operation:Ne,VideoPlay:ue,VideoPause:ge,Delete:ye},data(){return{isStarting:!1,isStopping:!1,isPaused:!1}},computed:{...V(["isDownloading","isLoggedIn","downloadSettings"]),canStart(){return!this.isDownloading&&this.isLoggedIn&&this.validationErrors.length===0},validationErrors(){if(!this.isLoggedIn)return["请先登录Pixiv账号"];const t=this.downloadSettings?.mode||"following",e=this.getCurrentSettings();try{const o=W.createUnifiedConfig(t,e);return W.validateConfig(o)}catch(o){return[o.message]}}},methods:{getCurrentDownloadPath(){switch(this.downloadSettings?.mode||"following"){case"following":return this.downloadSettings?.following?.downloadPath;case"search":return this.downloadSettings?.search?.downloadPath;case"ranking":return this.downloadSettings?.ranking?.downloadPath;case"artist":return this.downloadSettings?.artist?.downloadPath;default:return null}},getCurrentSettings(){const t=this.downloadSettings?.mode||"following";let e={};switch(t){case"following":e=this.downloadSettings?.following||{};break;case"search":e=this.downloadSettings?.search||{};break;case"ranking":e=this.downloadSettings?.ranking||{};break;case"artist":e=this.downloadSettings?.artist||{};break;default:e={}}return JSON.parse(JSON.stringify(e))},convertToBackendConfig(t,e){return W.createUnifiedConfig(t,e)},async startDownload(){if(!this.canStart){this.$message.warning("请检查配置后再开始下载");return}this.isStarting=!0;try{const t=this.downloadSettings?.mode||"following",e=this.getCurrentSettings(),o=this.convertToBackendConfig(t,e);console.log("🚀 准备开始下载，配置:",o);try{JSON.stringify(o),console.log("✅ 配置序列化验证通过")}catch(n){throw console.error("❌ 配置序列化失败:",n),new Error("配置包含不可序列化的对象: "+n.message)}console.log("📡 调用API开始下载...");const r=await this.$api.startDownload(o);if(r.success)this.$store.commit("setDownloading",!0),this.$store.commit("addLog","🚀 下载任务已提交到后台"),this.$message.success("下载任务已在后台启动，请查看日志获取进度");else throw new Error(r.message||"启动下载失败")}catch(t){const e=X.handleApiError(t,"下载启动");console.error("启动下载失败:",e.originalError),this.$message.error(e.message),this.$store.commit("addLog",`❌ ${e.message}`),X.isErrorType(t,"AUTH_ERROR")&&this.$router.push("/auth")}finally{this.isStarting=!1}},async stopDownload(){this.isStopping=!0;try{const t=await this.$api.stopDownload();if(t.success)this.$store.commit("setDownloading",!1),this.isPaused=!1,this.$store.commit("addLog","⏹️ 下载任务已停止"),this.$message.success("下载任务已停止");else throw new Error(t.message||"停止下载失败")}catch(t){const e=X.handleApiError(t,"停止下载");console.error("停止下载失败:",e.originalError),this.$message.error(e.message)}finally{this.isStopping=!1}},async pauseDownload(){try{if(this.isPaused){const t=await window.electronAPI.resumeDownload();t.success?(this.isPaused=!1,this.$store.commit("addLog","▶️ 下载任务已从断点继续"),this.$message.success("下载已从断点继续")):(this.$message.warning(t.message||"未找到有效断点，请重新开始下载"),this.$store.commit("addLog","⚠️ "+(t.message||"未找到有效断点")))}else{const t=await window.electronAPI.pauseDownload();if(t.success)this.isPaused=!0,this.$store.commit("addLog","⏸️ 下载任务已暂停，断点已保存"),this.$message.success("下载已暂停，断点已保存");else throw new Error(t.message||"暂停失败")}}catch(t){const e=this.isPaused?"继续下载失败":"暂停下载失败";this.$message.error(`${e}: ${t.message}`),this.$store.commit("addLog",`❌ ${e}: ${t.message}`)}},clearLogs(){this.$store.commit("clearLogs"),this.$message.success("日志已清空")}}},os={class:"card-header"},ns={class:"header-left"},as={class:"control-buttons modern-button-group"},ls={key:0,class:"validation-errors"},is={class:"error-list"};function rs(t,e,o,r,n,i){const y=c("Operation"),d=O,g=c("VideoPlay"),_=x,h=c("VideoPause"),C=c("Delete"),b=ce,v=N;return m(),p(v,{class:"control-card modern-card",shadow:"hover"},{header:a(()=>[l("div",os,[l("div",ns,[s(d,{class:"header-icon"},{default:a(()=>[s(y)]),_:1}),e[0]||(e[0]=l("span",{class:"header-title"},"下载控制",-1))])])]),default:a(()=>[l("div",as,[s(_,{type:"primary",size:"large",onClick:i.startDownload,disabled:!i.canStart,loading:n.isStarting,class:"control-btn start-btn"},{default:a(()=>[s(d,null,{default:a(()=>[s(g)]),_:1}),e[1]||(e[1]=l("span",null,"开始下载",-1))]),_:1,__:[1]},8,["onClick","disabled","loading"]),s(_,{type:"danger",size:"large",onClick:i.stopDownload,disabled:!t.isDownloading,loading:n.isStopping,class:"control-btn stop-btn"},{default:a(()=>[s(d,null,{default:a(()=>[s(h)]),_:1}),e[2]||(e[2]=l("span",null,"停止下载",-1))]),_:1,__:[2]},8,["onClick","disabled","loading"]),s(_,{type:"warning",size:"large",onClick:i.pauseDownload,disabled:!t.isDownloading||n.isPaused,class:"control-btn pause-btn"},{default:a(()=>[s(d,null,{default:a(()=>[n.isPaused?(m(),p(g,{key:1})):(m(),p(h,{key:0}))]),_:1}),l("span",null,S(n.isPaused?"继续":"暂停"),1)]),_:1},8,["onClick","disabled"]),s(_,{type:"info",size:"large",onClick:i.clearLogs,class:"control-btn clear-btn"},{default:a(()=>[s(d,null,{default:a(()=>[s(C)]),_:1}),e[3]||(e[3]=l("span",null,"清空日志",-1))]),_:1,__:[3]},8,["onClick"])]),i.validationErrors.length>0?(m(),P("div",ls,[s(b,{title:"配置检查",type:"warning",closable:!1,"show-icon":""},{default:a(()=>[l("ul",is,[(m(!0),P(F,null,le(i.validationErrors,R=>(m(),P("li",{key:R},S(R),1))),128))])]),_:1})])):T("",!0)]),_:1})}const ds=I(ss,[["render",rs],["__scopeId","data-v-79d83a7e"]]);const cs={name:"ProgressDisplay",components:{TrendCharts:Ue,Loading:Y,Download:ee,Timer:$e,Clock:ze,Odometer:Fe},data(){return{startTime:null,elapsedTime:0,timer:null,currentTask:"",downloadSpeed:"0 张/分钟"}},computed:{...V(["isDownloading","downloadProgress"]),progress(){const t=typeof this.downloadProgress=="object"?this.downloadProgress:{},e={current:t.current||0,total:t.total||0,percentage:t.percentage||0,message:t.message||""};return console.log("🎯 ProgressDisplay计算属性:",e,"原始数据:",this.downloadProgress),e},progressStatus(){return this.isDownloading?this.progress.percentage===100?"success":(this.progress.percentage>0,"normal"):"normal"},estimatedTime(){if(!this.isDownloading||this.progress.current===0)return 0;const t=this.progress.current/(this.elapsedTime/1e3);return(this.progress.total-this.progress.current)/t*1e3}},methods:{formatDuration(t){if(!t||t<=0)return"00:00:00";const e=Math.floor(t/1e3),o=Math.floor(e/3600),r=Math.floor(e%3600/60),n=e%60;return`${o.toString().padStart(2,"0")}:${r.toString().padStart(2,"0")}:${n.toString().padStart(2,"0")}`},updateElapsedTime(){if(this.startTime&&this.isDownloading&&(this.elapsedTime=Date.now()-this.startTime,this.elapsedTime>0)){const t=this.progress.current/(this.elapsedTime/1e3)*60;this.downloadSpeed=`${t.toFixed(1)} 张/分钟`}},startTimer(){this.startTime=Date.now(),this.timer=setInterval(this.updateElapsedTime,1e3)},stopTimer(){this.timer&&(clearInterval(this.timer),this.timer=null)},resetProgress(){this.startTime=null,this.elapsedTime=0,this.currentTask="",this.downloadSpeed="0 张/分钟",this.stopTimer()},updateCurrentTask(){this.isDownloading&&this.progress.current>0?this.progress.message?this.currentTask=this.progress.message:this.currentTask=`正在下载第 ${this.progress.current} 张图片...`:this.currentTask=""}},watch:{isDownloading(t){t?this.startTimer():this.stopTimer()},"progress.current"(){this.updateCurrentTask()},"progress.message"(){this.updateCurrentTask()}},mounted(){this.updateCurrentTask()},beforeUnmount(){this.stopTimer()}},us={class:"card-header"},gs={class:"header-left"},hs={class:"header-actions"},ms={class:"progress-content"},_s={class:"main-progress"},ps={class:"progress-info"},fs={class:"progress-text"},ws={class:"progress-percentage"},Ss={class:"progress-details"},ys={class:"detail-item"},vs={class:"detail-value"},ks={class:"detail-item"},Rs={class:"detail-value"},Cs={class:"detail-item"},bs={class:"detail-value"},Ts={class:"detail-item"},Es={class:"detail-value"};function Ps(t,e,o,r,n,i){const y=c("TrendCharts"),d=O,g=c("Loading"),_=We,h=c("Clock"),C=Z,b=c("Download"),v=c("Timer"),R=c("Odometer"),w=N;return m(),p(w,{class:"progress-card modern-card",shadow:"hover"},{header:a(()=>[l("div",us,[l("div",gs,[s(d,{class:"header-icon"},{default:a(()=>[s(y)]),_:1}),e[0]||(e[0]=l("span",{class:"header-title"},"下载进度",-1))]),l("div",hs,[t.isDownloading?(m(),p(_,{key:0,type:"success",effect:"light",class:"status-tag"},{default:a(()=>[s(d,{class:"rotating"},{default:a(()=>[s(g)]),_:1}),e[1]||(e[1]=f(" 下载中 ",-1))]),_:1,__:[1]})):(m(),p(_,{key:1,type:"info",effect:"light",class:"status-tag"},{default:a(()=>[s(d,null,{default:a(()=>[s(h)]),_:1}),e[2]||(e[2]=f(" 待机中 ",-1))]),_:1,__:[2]}))])])]),default:a(()=>[l("div",ms,[l("div",_s,[l("div",ps,[l("span",fs," 总进度: "+S(i.progress.current)+" / "+S(i.progress.total),1),l("span",ws,S(i.progress.percentage.toFixed(1))+"% ",1)]),s(C,{percentage:i.progress.percentage,status:i.progressStatus,"stroke-width":12,"show-text":!1,class:"modern-progress-bar"},null,8,["percentage","status"])]),l("div",Ss,[l("div",ys,[s(d,null,{default:a(()=>[s(b)]),_:1}),e[3]||(e[3]=l("span",{class:"detail-label"},"当前任务:",-1)),l("span",vs,S(n.currentTask||"无"),1)]),l("div",ks,[s(d,null,{default:a(()=>[s(v)]),_:1}),e[4]||(e[4]=l("span",{class:"detail-label"},"已用时间:",-1)),l("span",Rs,S(i.formatDuration(n.elapsedTime)),1)]),l("div",Cs,[s(d,null,{default:a(()=>[s(h)]),_:1}),e[5]||(e[5]=l("span",{class:"detail-label"},"预计剩余:",-1)),l("span",bs,S(i.formatDuration(i.estimatedTime)),1)]),l("div",Ts,[s(d,null,{default:a(()=>[s(R)]),_:1}),e[6]||(e[6]=l("span",{class:"detail-label"},"下载速度:",-1)),l("span",Es,S(n.downloadSpeed),1)])])])]),_:1})}const Ds=I(cs,[["render",Ps],["__scopeId","data-v-1f39e3aa"]]);const Is={name:"StatsDisplay",components:{DataAnalysis:Be,RefreshLeft:Ge,SuccessFilled:B,CircleCloseFilled:J,WarningFilled:H,DataLine:Ke},data(){return{totalRetries:0,totalBytes:0,averageSpeed:0,maxConcurrent:3}},computed:{...V(["isDownloading","downloadStats","downloadSettings"]),stats(){const t={downloaded:this.downloadStats.success||this.downloadStats.completed||0,failed:this.downloadStats.failed||0,skipped:this.downloadStats.skipped||0,total:this.downloadStats.total||0};return console.log("📈 StatsDisplay计算属性:",t,"原始数据:",this.downloadStats),t},totalProcessed(){return this.stats.downloaded+this.stats.failed+this.stats.skipped},successRate(){return this.totalProcessed===0?0:Math.round(this.stats.downloaded/this.totalProcessed*100)},successRateColor(){return this.successRate>=90?"var(--success-color)":this.successRate>=70?"var(--warning-color)":"var(--error-color)"}},methods:{resetStats(){this.$store.commit("updateDownloadStats",{downloaded:0,failed:0,skipped:0}),this.totalRetries=0,this.totalBytes=0,this.averageSpeed=0,this.$message.success("统计数据已重置")},formatBytes(t){if(t===0)return"0 B";const e=1024,o=["B","KB","MB","GB","TB"],r=Math.floor(Math.log(t)/Math.log(e));return parseFloat((t/Math.pow(e,r)).toFixed(2))+" "+o[r]},updateAverageSpeed(){this.isDownloading&&this.stats.downloaded>0&&(this.averageSpeed=Math.random()*10+5)},simulateDataTransfer(){this.isDownloading&&(this.totalBytes+=Math.random()*1024*1024)}},mounted(){setInterval(()=>{this.updateAverageSpeed(),this.simulateDataTransfer()},2e3),this.maxConcurrent=this.downloadSettings.maxConcurrent||3},watch:{"downloadSettings.maxConcurrent"(t){this.maxConcurrent=t}}},Ls={class:"card-header"},Os={class:"header-left"},As={class:"header-actions"},Ms={class:"stats-content"},Vs={class:"stats-grid"},xs={class:"stat-item success"},Ns={class:"stat-icon"},Us={class:"stat-content"},$s={class:"stat-value"},zs={class:"stat-item danger"},Fs={class:"stat-icon"},Ws={class:"stat-content"},Bs={class:"stat-value"},Gs={class:"stat-item warning"},Ks={class:"stat-icon"},Hs={class:"stat-content"},Ys={class:"stat-value"},Js={class:"stat-item info"},Qs={class:"stat-icon"},js={class:"stat-content"},Xs={class:"stat-value"},qs={class:"success-rate"},Zs={class:"rate-header"},eo={class:"rate-value"},to={class:"detailed-stats"};function so(t,e,o,r,n,i){const y=c("DataAnalysis"),d=O,g=c("RefreshLeft"),_=x,h=c("SuccessFilled"),C=c("CircleCloseFilled"),b=c("WarningFilled"),v=c("DataLine"),R=Z,w=He,k=Ye,L=N;return m(),p(L,{class:"stats-card modern-card",shadow:"hover"},{header:a(()=>[l("div",Ls,[l("div",Os,[s(d,{class:"header-icon"},{default:a(()=>[s(y)]),_:1}),e[0]||(e[0]=l("span",{class:"header-title"},"下载统计",-1))]),l("div",As,[s(_,{size:"small",onClick:i.resetStats,disabled:t.isDownloading,class:"modern-button"},{default:a(()=>[s(d,null,{default:a(()=>[s(g)]),_:1}),e[1]||(e[1]=f(" 重置 ",-1))]),_:1,__:[1]},8,["onClick","disabled"])])])]),default:a(()=>[l("div",Ms,[l("div",Vs,[l("div",xs,[l("div",Ns,[s(d,null,{default:a(()=>[s(h)]),_:1})]),l("div",Us,[l("div",$s,S(i.stats.downloaded),1),e[2]||(e[2]=l("div",{class:"stat-label"},"已下载",-1))])]),l("div",zs,[l("div",Fs,[s(d,null,{default:a(()=>[s(C)]),_:1})]),l("div",Ws,[l("div",Bs,S(i.stats.failed),1),e[3]||(e[3]=l("div",{class:"stat-label"},"失败",-1))])]),l("div",Gs,[l("div",Ks,[s(d,null,{default:a(()=>[s(b)]),_:1})]),l("div",Hs,[l("div",Ys,S(i.stats.skipped),1),e[4]||(e[4]=l("div",{class:"stat-label"},"跳过",-1))])]),l("div",Js,[l("div",Qs,[s(d,null,{default:a(()=>[s(v)]),_:1})]),l("div",js,[l("div",Xs,S(i.totalProcessed),1),e[5]||(e[5]=l("div",{class:"stat-label"},"总计",-1))])])]),l("div",qs,[l("div",Zs,[e[6]||(e[6]=l("span",{class:"rate-label"},"成功率",-1)),l("span",eo,S(i.successRate)+"%",1)]),s(R,{percentage:i.successRate,color:i.successRateColor,"stroke-width":8,"show-text":!1},null,8,["percentage","color"])]),l("div",to,[s(k,{column:2,size:"small",border:""},{default:a(()=>[s(w,{label:"平均速度"},{default:a(()=>[f(S(n.averageSpeed)+" 张/分钟 ",1)]),_:1}),s(w,{label:"最大并发"},{default:a(()=>[f(S(n.maxConcurrent),1)]),_:1}),s(w,{label:"重试次数"},{default:a(()=>[f(S(n.totalRetries),1)]),_:1}),s(w,{label:"数据传输"},{default:a(()=>[f(S(i.formatBytes(n.totalBytes)),1)]),_:1})]),_:1})])])]),_:1})}const oo=I(Is,[["render",so],["__scopeId","data-v-30428466"]]);const no={name:"LogDisplay",components:{Document:Je,Bottom:Qe,Download:ee,Delete:ye,Search:je,InfoFilled:q,WarningFilled:H,CircleCloseFilled:J,SuccessFilled:B,QuestionFilled:ne},data(){return{searchText:"",filterLevel:"all",autoScroll:!0,lastUpdateTime:""}},computed:{...V(["logs"]),filteredLogs(){let t=this.logs;return this.searchText&&(t=t.filter(e=>e.message.toLowerCase().includes(this.searchText.toLowerCase()))),this.filterLevel!=="all"&&(t=t.filter(e=>this.getLogType(e.message)===this.filterLevel)),t}},methods:{getLogType(t){const e=t.toLowerCase();return t.includes("❌")||t.includes("错误")||t.includes("失败")||t.includes("ERROR")||t.includes("CRITICAL")||e.includes("error")||e.includes("critical")||t.includes("Python Backend Error")?"error":t.includes("⚠️")||t.includes("警告")||t.includes("WARNING")||e.includes("warning")||t.includes("Python Backend Warning")?"warning":t.includes("✅")||t.includes("成功")||t.includes("完成")||t.includes("已完成")||t.includes("SUCCESS")||e.includes("success")?"success":"info"},getLogIcon(t){switch(this.getLogType(t)){case"error":return J;case"warning":return H;case"success":return B;case"info":return q;default:return ne}},scrollToBottom(){this.$nextTick(()=>{const t=this.$refs.scrollbar;t&&t.setScrollTop(t.wrapRef.scrollHeight)})},clearLogs(){this.$store.commit("clearLogs"),this.lastUpdateTime=new Date().toLocaleTimeString()},async exportLogs(){try{const t=this.filteredLogs.map(o=>`[${o.timestamp}] ${o.message}`).join(`
`),e=await window.electronAPI.showSaveDialog({title:"导出日志",defaultPath:`pixiv-spider-logs-${new Date().toISOString().split("T")[0]}.txt`,filters:[{name:"文本文件",extensions:["txt"]},{name:"所有文件",extensions:["*"]}]});!e.canceled&&e.filePath&&this.$message.success("日志导出成功")}catch{this.$message.error("导出日志失败")}},updateLastTime(){this.lastUpdateTime=new Date().toLocaleTimeString()}},watch:{logs:{handler(t){this.updateLastTime(),this.autoScroll&&t.length>0&&this.$nextTick(()=>{this.scrollToBottom()})},deep:!0}},mounted(){this.updateLastTime(),setTimeout(()=>{this.$store.commit("addLog","🚀 Pixiv Spider 启动成功"),this.$store.commit("addLog","🔧 正在初始化配置..."),this.$store.commit("addLog","✅ 配置加载完成")},1e3)}},ao={class:"card-header log-header"},lo={class:"header-left"},io={class:"header-actions"},ro={class:"log-content"},co={class:"log-filters"},uo={class:"log-container",ref:"logContainer"},go={class:"log-list"},ho={class:"log-time"},mo={class:"log-message"},_o={class:"log-text"},po={key:0,class:"empty-logs"},fo={class:"log-stats"};function wo(t,e,o,r,n,i){const y=c("Document"),d=O,g=c("Bottom"),_=x,h=c("Download"),C=c("Delete"),b=ve,v=c("Search"),R=fe,w=he,k=me,L=te,A=Xe,M=ke,$=se,z=N;return m(),p(z,{class:"log-card modern-card modern-log",shadow:"hover"},{header:a(()=>[l("div",ao,[l("div",lo,[s(d,{class:"header-icon"},{default:a(()=>[s(y)]),_:1}),e[3]||(e[3]=l("span",{class:"header-title"},"运行日志",-1))]),l("div",io,[s(b,{size:"small",class:"modern-button-group"},{default:a(()=>[s(_,{onClick:i.scrollToBottom,disabled:t.logs.length===0},{default:a(()=>[s(d,null,{default:a(()=>[s(g)]),_:1}),e[4]||(e[4]=f(" 底部 ",-1))]),_:1,__:[4]},8,["onClick","disabled"]),s(_,{onClick:i.exportLogs,disabled:t.logs.length===0},{default:a(()=>[s(d,null,{default:a(()=>[s(h)]),_:1}),e[5]||(e[5]=f(" 导出 ",-1))]),_:1,__:[5]},8,["onClick","disabled"]),s(_,{onClick:i.clearLogs,disabled:t.logs.length===0},{default:a(()=>[s(d,null,{default:a(()=>[s(C)]),_:1}),e[6]||(e[6]=f(" 清空 ",-1))]),_:1,__:[6]},8,["onClick","disabled"])]),_:1})])])]),default:a(()=>[l("div",ro,[l("div",co,[s(R,{modelValue:n.searchText,"onUpdate:modelValue":e[0]||(e[0]=E=>n.searchText=E),placeholder:"搜索日志...",clearable:"",size:"small",style:{width:"200px"}},{prefix:a(()=>[s(d,null,{default:a(()=>[s(v)]),_:1})]),_:1},8,["modelValue"]),s(k,{modelValue:n.filterLevel,"onUpdate:modelValue":e[1]||(e[1]=E=>n.filterLevel=E),placeholder:"日志级别",size:"small",style:{width:"120px"}},{default:a(()=>[s(w,{label:"全部",value:"all"}),s(w,{label:"信息",value:"info"}),s(w,{label:"警告",value:"warning"}),s(w,{label:"错误",value:"error"}),s(w,{label:"成功",value:"success"})]),_:1},8,["modelValue"]),s(L,{modelValue:n.autoScroll,"onUpdate:modelValue":e[2]||(e[2]=E=>n.autoScroll=E),"active-text":"自动滚动","inactive-text":"",size:"small"},null,8,["modelValue"])]),l("div",uo,[s(M,{ref:"scrollbar",height:"400px"},{default:a(()=>[l("div",go,[(m(!0),P(F,null,le(i.filteredLogs,E=>(m(),P("div",{key:E.id,class:K(["log-item",`log-${i.getLogType(E.message)}`])},[l("div",ho,S(E.timestamp),1),l("div",mo,[s(d,{class:K(`log-icon-${i.getLogType(E.message)}`)},{default:a(()=>[(m(),p(ae(i.getLogIcon(E.message))))]),_:2},1032,["class"]),l("span",_o,S(E.message),1)])],2))),128)),i.filteredLogs.length===0?(m(),P("div",po,[s(A,{description:"暂无日志","image-size":80},{image:a(()=>[s(d,{size:"80",style:{color:"var(--text-muted)"}},{default:a(()=>[s(y)]),_:1})]),_:1})])):T("",!0)])]),_:1},512)],512),l("div",fo,[s($,{size:"small",type:"info"},{default:a(()=>[f(" 总计: "+S(t.logs.length)+" 条 | 显示: "+S(i.filteredLogs.length)+" 条 | 最后更新: "+S(n.lastUpdateTime),1)]),_:1})])])]),_:1})}const So=I(no,[["render",wo],["__scopeId","data-v-a35235b1"]]);const yo={name:"Home",components:{Picture:qe,Sunny:Ze,Moon:et,Monitor:ie,DocumentAdd:tt,Minus:st,FullScreen:ot,Close:re,LoginStatus:Pt,DownloadSettings:Kt,PerformanceSettings:ts,ControlButtons:ds,ProgressDisplay:Ds,StatsDisplay:oo,LogDisplay:So},data(){return{leftPanelWidth:400,isResizing:!1,startX:0,startWidth:0}},computed:{currentTheme(){return this.$store.state.theme},isDarkMode(){return this.currentTheme==="auto"?this.getSystemTheme()==="dark":this.currentTheme==="dark"},isAutoTheme(){return this.currentTheme==="auto"}},async mounted(){await this.$store.dispatch("loadSettings"),this.loadThemeSettings(),this.setupSystemThemeListener();const t=localStorage.getItem("leftPanelWidth");t&&(this.leftPanelWidth=parseInt(t))},methods:{async saveSettings(){try{console.log("🔄 开始保存设置..."),await this.$store.dispatch("saveSettings"),this.$message.success("设置已保存"),console.log("✅ 设置保存成功")}catch(t){console.error("❌ 保存设置失败:",t),this.$message.error("保存设置失败: "+t.message)}},startResize(t){this.isResizing=!0,this.startX=t.clientX,this.startWidth=this.leftPanelWidth,document.addEventListener("mousemove",this.doResize),document.addEventListener("mouseup",this.stopResize),document.body.style.userSelect="none",document.body.style.cursor="col-resize"},doResize(t){if(!this.isResizing)return;const e=t.clientX-this.startX,o=this.startWidth+e,r=280,n=window.innerWidth*.6;this.leftPanelWidth=Math.max(r,Math.min(n,o))},stopResize(){this.isResizing=!1,document.removeEventListener("mousemove",this.doResize),document.removeEventListener("mouseup",this.stopResize),document.body.style.userSelect="",document.body.style.cursor="",localStorage.setItem("leftPanelWidth",this.leftPanelWidth.toString())},setTheme(t){console.log("🎨 setTheme 被调用，参数:",t),console.log("🎨 当前store状态:",this.$store.state.theme),this.$store.commit("setTheme",t),console.log("🎨 commit后store状态:",this.$store.state.theme),this.$store.dispatch("saveSettings"),this.applyTheme(),this.$message.success(`主题已切换为: ${t}`)},getSystemTheme(){return window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"},setupSystemThemeListener(){window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",()=>{this.currentTheme==="auto"&&this.applyTheme()})},applyTheme(){let t="light";this.currentTheme==="dark"?t="dark":this.currentTheme==="light"?t="light":this.currentTheme==="auto"&&(t=this.getSystemTheme()),console.log("🎨 应用主题:",t,"当前主题设置:",this.currentTheme),document.documentElement.setAttribute("data-theme",t),document.body.setAttribute("data-theme",t),document.body.classList.remove("dark-theme","light-theme","dark"),document.documentElement.classList.remove("dark-theme","light-theme","dark"),t==="dark"?(document.body.classList.add("dark-theme","dark"),document.documentElement.classList.add("dark-theme","dark")):(document.body.classList.add("light-theme"),document.documentElement.classList.add("light-theme")),this.$nextTick(()=>{const e=document.documentElement.getAttribute("data-theme");console.log("✅ 主题应用完成，实际data-theme属性:",e),console.log("🔍 body背景色:",window.getComputedStyle(document.body).backgroundColor),window.electronAPI&&window.electronAPI.setTitle&&window.electronAPI.setTitle(`Pixiv Spider - ${e} 主题`)})},loadThemeSettings(){this.applyTheme()},minimizeWindow(){window.electronAPI&&window.electronAPI.minimizeWindow&&window.electronAPI.minimizeWindow()},maximizeWindow(){window.electronAPI&&window.electronAPI.maximizeWindow&&window.electronAPI.maximizeWindow()},closeWindow(){window.electronAPI&&window.electronAPI.closeWindow&&window.electronAPI.closeWindow()}}},vo={class:"custom-titlebar"},ko={class:"titlebar-content"},Ro={class:"titlebar-left"},Co={class:"titlebar-right"},bo={class:"window-controls"},To={class:"main-container"},Eo={class:"config-content"},Po={class:"content-container"},Do={class:"progress-section"},Io={class:"log-section"};function Lo(t,e,o,r,n,i){const y=c("Picture"),d=O,g=c("Sunny"),_=x,h=c("Moon"),C=c("Monitor"),b=ve,v=c("DocumentAdd"),R=c("Minus"),w=c("FullScreen"),k=c("Close"),L=c("LoginStatus"),A=c("DownloadSettings"),M=c("PerformanceSettings"),$=c("ControlButtons"),z=ke,E=c("ProgressDisplay"),Q=c("StatsDisplay"),j=c("LogDisplay"),u=nt;return m(),p(u,{class:K(["app-container",{"dark-theme":i.isDarkMode}])},{default:a(()=>[l("div",vo,[l("div",ko,[l("div",Ro,[s(d,{class:"app-icon"},{default:a(()=>[s(y)]),_:1}),e[7]||(e[7]=l("span",{class:"app-name"},"Pixiv Spider",-1))]),e[12]||(e[12]=l("div",{class:"titlebar-center"},null,-1)),l("div",Co,[s(b,{class:"theme-switcher"},{default:a(()=>[s(_,{type:i.currentTheme==="light"?"primary":"",onClick:e[0]||(e[0]=D=>i.setTheme("light")),size:"small"},{default:a(()=>[s(d,null,{default:a(()=>[s(g)]),_:1}),e[8]||(e[8]=f(" 浅色 ",-1))]),_:1,__:[8]},8,["type"]),s(_,{type:i.currentTheme==="dark"?"primary":"",onClick:e[1]||(e[1]=D=>i.setTheme("dark")),size:"small"},{default:a(()=>[s(d,null,{default:a(()=>[s(h)]),_:1}),e[9]||(e[9]=f(" 深色 ",-1))]),_:1,__:[9]},8,["type"]),s(_,{type:i.currentTheme==="auto"?"primary":"",onClick:e[2]||(e[2]=D=>i.setTheme("auto")),size:"small"},{default:a(()=>[s(d,null,{default:a(()=>[s(C)]),_:1}),e[10]||(e[10]=f(" 自动 ",-1))]),_:1,__:[10]},8,["type"])]),_:1}),s(_,{type:"primary",onClick:i.saveSettings,size:"small"},{default:a(()=>[s(d,null,{default:a(()=>[s(v)]),_:1}),e[11]||(e[11]=f(" 保存设置 ",-1))]),_:1,__:[11]},8,["onClick"]),l("div",bo,[l("button",{class:"window-control minimize",onClick:e[3]||(e[3]=(...D)=>i.minimizeWindow&&i.minimizeWindow(...D))},[s(d,null,{default:a(()=>[s(R)]),_:1})]),l("button",{class:"window-control maximize",onClick:e[4]||(e[4]=(...D)=>i.maximizeWindow&&i.maximizeWindow(...D))},[s(d,null,{default:a(()=>[s(w)]),_:1})]),l("button",{class:"window-control close",onClick:e[5]||(e[5]=(...D)=>i.closeWindow&&i.closeWindow(...D))},[s(d,null,{default:a(()=>[s(k)]),_:1})])])])])]),l("div",To,[l("div",{class:"config-panel",style:oe({width:n.leftPanelWidth+"px"})},[s(z,null,{default:a(()=>[l("div",Eo,[s(L),s(A),s(M),s($)])]),_:1})],4),l("div",{class:K(["resizer",{resizing:n.isResizing}]),onMousedown:e[6]||(e[6]=(...D)=>i.startResize&&i.startResize(...D))},e[13]||(e[13]=[l("div",{class:"resizer-line"},null,-1)]),34),l("div",{class:"main-content",style:oe({width:"calc(100% - "+(n.leftPanelWidth+8)+"px)"})},[l("div",Po,[l("div",Do,[s(E,{class:"modern-progress"}),s(Q)]),l("div",Io,[s(j)])])],4)])]),_:1},8,["class"])}const Oo=I(yo,[["render",Lo],["__scopeId","data-v-5b693db0"]]),G=Re({state:{isLoggedIn:!1,loginStatus:"检查中...",userInfo:null,isDownloading:!1,downloadStatus:"idle",downloadProgress:{current:0,total:0,percentage:0,message:""},downloadStats:{total:0,completed:0,failed:0,skipped:0,success:0,speed:0},isConnected:!1,backendReady:!1,theme:"auto",downloadSettings:{mode:"following",following:{downloadType:"pages",days:7,pageStart:1,pageEnd:5,downloadPath:""},search:{keyword:"",searchType:"artworks",bookmarkCount:1e3,searchMode:"all",pageStart:1,pageEnd:5,downloadPath:""},ranking:{rankingType:"overall",rankingPeriod:"daily",rankingMode:"safe",rankingDate:(()=>{const t=new Date;return t.getFullYear()+"-"+String(t.getMonth()+1).padStart(2,"0")+"-"+String(t.getDate()).padStart(2,"0")})(),downloadPath:""},artist:{artistId:"",pageStart:1,pageEnd:5,downloadPath:""},maxConcurrent:3,delay:1e3,retryCount:3,timeout:30,skipExisting:!0,saveOriginal:!0,createSubfolder:!0},logs:[],config:{}},mutations:{setLoginStatus(t,{isLoggedIn:e,status:o,userInfo:r}){t.isLoggedIn=e,o&&(t.loginStatus=o),r&&(t.userInfo=r)},updateDownloadState(t,{status:e,progress:o,stats:r}){e!==void 0&&(t.downloadStatus=e),o!==void 0&&(t.downloadProgress={...t.downloadProgress,...o}),r!==void 0&&(t.downloadStats={...t.downloadStats,...r})},setConnectionStatus(t,e){t.isConnected=e},setBackendReady(t,e){t.backendReady=e},setConfig(t,e){t.config={...t.config,...e}},setTheme(t,e){t.theme=e,console.log("🎨 主题已更新为:",e)},updateDownloadSettings(t,e){console.log("🔄 更新下载设置:",e),console.log("🔄 当前state.downloadSettings:",t.downloadSettings),e.following&&(t.downloadSettings.following={...t.downloadSettings.following,...e.following},console.log("🔄 更新following设置:",t.downloadSettings.following)),e.search&&(t.downloadSettings.search={...t.downloadSettings.search,...e.search},console.log("🔄 更新search设置:",t.downloadSettings.search)),e.ranking&&(t.downloadSettings.ranking={...t.downloadSettings.ranking,...e.ranking},console.log("🔄 更新ranking设置:",t.downloadSettings.ranking)),e.artist&&(t.downloadSettings.artist={...t.downloadSettings.artist,...e.artist},console.log("🔄 更新artist设置:",t.downloadSettings.artist)),Object.keys(e).forEach(o=>{["following","search","ranking","artist"].includes(o)||(t.downloadSettings[o]=e[o],console.log(`🔄 更新${o}设置:`,e[o]))}),console.log("✅ 更新后的downloadSettings:",t.downloadSettings)},setDownloading(t,e){t.isDownloading=e},addLog(t,e){const o=typeof e=="string"?e:String(e);t.logs.push({id:Date.now()+Math.random(),timestamp:new Date().toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"}),message:o.trim(),type:"info"}),t.logs.length>1e3&&(t.logs=t.logs.slice(-500))},clearLogs(t){t.logs=[]}},actions:{async initializeApp({commit:t,dispatch:e}){try{t("setConnectionStatus",!0),t("setBackendReady",!0),await e("checkLoginStatus"),await e("loadConfig")}catch(o){console.error("初始化应用失败:",o),t("setConnectionStatus",!1)}},async checkLoginStatus({commit:t}){try{const e=window.electronAPI;if(!e)throw new Error("API not available");const o=await e.getAuthStatus();return t("setLoginStatus",{isLoggedIn:o.authenticated||!1,status:o.authenticated?"已登录":"未登录",userInfo:o.user_info}),o}catch(e){throw console.error("检查登录状态失败:",e),t("setLoginStatus",{isLoggedIn:!1,status:"连接失败"}),e}},async loadConfig({commit:t}){try{const e=window.electronAPI;if(!e)throw new Error("API not available");const o=await e.getConfig();return o.success&&t("setConfig",o.config),o}catch(e){throw console.error("加载配置失败:",e),e}},updateDownloadStatus({commit:t},e){t("updateDownloadState",e)},async loadSettings({commit:t}){try{if(console.log("📥 开始加载设置..."),window.electronAPI&&window.electronAPI.getStoreValue){console.log("📥 使用Electron API加载设置");const e=await window.electronAPI.getStoreValue("downloadSettings");console.log("📥 从Electron Store加载的设置:",e),e?(t("updateDownloadSettings",e),console.log("✅ 下载设置已加载")):console.log("⚠️ 没有找到保存的下载设置");const o=await window.electronAPI.getStoreValue("theme");console.log("📥 从Electron Store加载的主题:",o),o?(t("setTheme",o),console.log("✅ 主题设置已加载")):console.log("⚠️ 没有找到保存的主题设置")}else{console.log("📥 使用localStorage加载设置");const e=localStorage.getItem("downloadSettings");console.log("📥 从localStorage加载的设置:",e),e&&(t("updateDownloadSettings",JSON.parse(e)),console.log("✅ 下载设置已从localStorage加载"));const o=localStorage.getItem("pixiv-spider-theme");console.log("📥 从localStorage加载的主题:",o),o&&(t("setTheme",o),console.log("✅ 主题设置已从localStorage加载"))}console.log("✅ 设置加载完成")}catch(e){console.error("❌ 加载设置失败:",e)}},async saveSettings({state:t}){try{console.log("💾 Store保存设置:",t.downloadSettings);const e=JSON.parse(JSON.stringify(t.downloadSettings)),o=String(t.theme);console.log("💾 清理后的设置:",e),window.electronAPI&&window.electronAPI.setStoreValue?(await window.electronAPI.setStoreValue("downloadSettings",e),await window.electronAPI.setStoreValue("theme",o),console.log("✅ 设置已保存到Electron Store")):(localStorage.setItem("downloadSettings",JSON.stringify(e)),localStorage.setItem("pixiv-spider-theme",o),console.log("✅ 设置已保存到localStorage"))}catch(e){throw console.error("❌ Store保存设置失败:",e),console.error("❌ 错误详情:",e.stack),e}}},getters:{isAuthenticated:t=>t.isLoggedIn,isAppReady:t=>t.isConnected&&t.backendReady,downloadProgressPercent:t=>Math.round(t.downloadProgress*100)/100,downloadStatsText:t=>{const{total:e,completed:o,failed:r}=t.downloadStats;return`总计: ${e} | 完成: ${o} | 失败: ${r}`}}});class Ao{constructor(){if(!window.electronAPI)throw new Error("Electron API not available");this.electronAPI=window.electronAPI}async healthCheck(){return this.electronAPI.healthCheck()}async checkConnection(){try{return await this.healthCheck(),!0}catch(e){return console.error("❌ 后端连接检查失败:",e),!1}}async getAuthStatus(){return this.electronAPI.getAuthStatus()}async login(e=!1){return this.electronAPI.login(e)}async checkCookies(){return this.electronAPI.checkCookies()}async startSeleniumLogin(){return this.electronAPI.startSeleniumLogin()}async confirmLogin(){return this.electronAPI.confirmLogin()}async cancelLogin(){return this.electronAPI.cancelLogin()}async startDownload(e){return this.electronAPI.startDownload(e)}async stopDownload(){return this.electronAPI.stopDownload()}async pauseDownload(){return this.electronAPI.pauseDownload()}async resumeDownload(){return this.electronAPI.resumeDownload()}async getDownloadStatus(){return this.electronAPI.getDownloadStatus()}async getConfig(){return this.electronAPI.getConfig()}async updateConfig(e){return this.electronAPI.updateConfig(e)}}const Mo=new Ao;class Vo{constructor(){if(this.listeners=new Map,this._isConnected=!1,!window.electronAPI)throw new Error("Electron API not available");this.electronAPI=window.electronAPI,this._setupEventListeners()}_setupEventListeners(){this.electronAPI.onBackendReady(()=>{console.log("🔗 Backend connected via IPC"),this._isConnected=!0,this.emit("connected")}),this.electronAPI.onBackendError(e=>{console.error("❌ Backend error:",e),this._isConnected=!1,this.emit("error",e)}),this.electronAPI.onStatusUpdate(e=>{this.emit("statusUpdate",e)}),this.electronAPI.onProgressUpdate(e=>{this.emit("progressUpdate",e)}),this.electronAPI.onDownloadComplete(e=>{this.emit("downloadComplete",e)}),this.electronAPI.onAuthStatus(e=>{this.emit("authStatus",e)}),this.electronAPI.onLogMessage(e=>{this.emit("logMessage",e)})}on(e,o){this.listeners.has(e)||this.listeners.set(e,[]),this.listeners.get(e).push(o)}off(e,o){if(this.listeners.has(e)){const r=this.listeners.get(e),n=r.indexOf(o);n>-1&&r.splice(n,1)}}removeAllListeners(e){e?this.listeners.delete(e):this.listeners.clear()}emit(e,o){this.listeners.has(e)&&this.listeners.get(e).forEach(r=>{try{r(o)}catch(n){console.error("❌ 事件回调执行失败:",n)}})}get isConnected(){return this._isConnected}destroy(){this.listeners.clear(),this._isConnected=!1}}const xo=new Vo,No=[{path:"/",name:"Home",component:Oo}],Uo=Ce({history:be(),routes:No}),U=Te(ct);for(const[t,e]of Object.entries(at))U.component(t,e);U.provide("$api",Mo);U.provide("$ipc",xo);U.use(Uo);U.use(G);U.use(lt);G.dispatch("initializeApp").catch(t=>{console.error("应用初始化失败:",t)});G.dispatch("loadSettings").then(()=>{const t=()=>{const e=G.state.theme;let o="light";e==="dark"?o="dark":e==="light"?o="light":e==="auto"&&(o=window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),document.documentElement.setAttribute("data-theme",o),document.body.setAttribute("data-theme",o),document.body.classList.remove("dark-theme","light-theme","dark"),document.documentElement.classList.remove("dark-theme","light-theme","dark"),o==="dark"?(document.body.classList.add("dark-theme","dark"),document.documentElement.classList.add("dark-theme","dark")):(document.body.classList.add("light-theme"),document.documentElement.classList.add("light-theme"))};t(),window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",()=>{G.state.theme==="auto"&&t()})});U.mount("#app");
