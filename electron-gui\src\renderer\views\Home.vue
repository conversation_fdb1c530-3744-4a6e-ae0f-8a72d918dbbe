<template>
  <el-container class="app-container" :class="{ 'dark-theme': isDarkMode }">
    <!-- 自定义标题栏 -->
    <div class="custom-titlebar">
      <div class="titlebar-content">
        <div class="titlebar-left">
          <el-icon class="app-icon"><Picture /></el-icon>
          <span class="app-name">Pixiv Spider</span>
        </div>
        <div class="titlebar-center">
          <!-- 空白区域，用于拖拽 -->
        </div>
        <div class="titlebar-right">
          <!-- 主题切换按钮 -->
          <el-button-group class="theme-switcher">
            <el-button
              :type="currentTheme === 'light' ? 'primary' : ''"
              @click="setTheme('light')"
              size="small"
            >
              <el-icon><Sunny /></el-icon>
              浅色
            </el-button>
            <el-button
              :type="currentTheme === 'dark' ? 'primary' : ''"
              @click="setTheme('dark')"
              size="small"
            >
              <el-icon><Moon /></el-icon>
              深色
            </el-button>
            <el-button
              :type="currentTheme === 'auto' ? 'primary' : ''"
              @click="setTheme('auto')"
              size="small"
            >
              <el-icon><Monitor /></el-icon>
              自动
            </el-button>
          </el-button-group>

          <el-button type="primary" @click="saveSettings" size="small">
            <el-icon><DocumentAdd /></el-icon>
            保存设置
          </el-button>
          <!-- 窗口控制按钮 -->
          <div class="window-controls">
            <button class="window-control minimize" @click="minimizeWindow">
              <el-icon><Minus /></el-icon>
            </button>
            <button class="window-control maximize" @click="maximizeWindow">
              <el-icon><FullScreen /></el-icon>
            </button>
            <button class="window-control close" @click="closeWindow">
              <el-icon><Close /></el-icon>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主体内容 -->
    <div class="main-container">
      <!-- 左侧配置面板 -->
      <div class="config-panel" :style="{ width: leftPanelWidth + 'px' }">
        <el-scrollbar>
          <div class="config-content">
            <!-- 登录状态 -->
            <LoginStatus />

            <!-- 下载设置 -->
            <DownloadSettings />

            <!-- 性能设置 -->
            <PerformanceSettings />

            <!-- 控制按钮 -->
            <ControlButtons />
          </div>
        </el-scrollbar>
      </div>

      <!-- 可拖动分隔条 -->
      <div
        class="resizer"
        @mousedown="startResize"
        :class="{ 'resizing': isResizing }"
      >
        <div class="resizer-line"></div>
      </div>

      <!-- 右侧主要内容 -->
      <div class="main-content" :style="{ width: 'calc(100% - ' + (leftPanelWidth + 8) + 'px)' }">
        <div class="content-container">
          <!-- 进度和统计 -->
          <div class="progress-section">
            <ProgressDisplay class="modern-progress" />
            <StatsDisplay />
          </div>

          <!-- 日志区域 -->
          <div class="log-section">
            <LogDisplay />
          </div>
        </div>
      </div>
    </div>
  </el-container>
</template>

<script>
import {
  Picture,
  Sunny,
  Moon,
  Monitor,
  DocumentAdd,
  Minus,
  FullScreen,
  Close
} from '@element-plus/icons-vue'
import LoginStatus from '../components/LoginStatus.vue'
import DownloadSettings from '../components/DownloadSettings.vue'
import PerformanceSettings from '../components/PerformanceSettings.vue'
import ControlButtons from '../components/ControlButtons.vue'
import ProgressDisplay from '../components/ProgressDisplay.vue'
import StatsDisplay from '../components/StatsDisplay.vue'
import LogDisplay from '../components/LogDisplay.vue'

export default {
  name: 'Home',
  components: {
    Picture,
    Sunny,
    Moon,
    Monitor,
    DocumentAdd,
    Minus,
    FullScreen,
    Close,
    LoginStatus,
    DownloadSettings,
    PerformanceSettings,
    ControlButtons,
    ProgressDisplay,
    StatsDisplay,
    LogDisplay
  },
  data() {
    return {
      leftPanelWidth: 400, // 左侧面板宽度
      isResizing: false,   // 是否正在拖动
      startX: 0,           // 拖动开始的X坐标
      startWidth: 0        // 拖动开始时的宽度
    }
  },
  computed: {
    currentTheme() {
      return this.$store.state.theme
    },
    isDarkMode() {
      if (this.currentTheme === 'auto') {
        return this.getSystemTheme() === 'dark'
      }
      return this.currentTheme === 'dark'
    },
    isAutoTheme() {
      return this.currentTheme === 'auto'
    }
  },
  async mounted() {
    // 加载设置
    await this.$store.dispatch('loadSettings')
    this.loadThemeSettings()
    this.setupSystemThemeListener()

    // 加载保存的面板宽度
    const savedWidth = localStorage.getItem('leftPanelWidth')
    if (savedWidth) {
      this.leftPanelWidth = parseInt(savedWidth)
    }
  },
  methods: {
    async saveSettings() {
      try {
        console.log('🔄 开始保存设置...')
        await this.$store.dispatch('saveSettings')
        this.$message.success('设置已保存')
        console.log('✅ 设置保存成功')
      } catch (error) {
        console.error('❌ 保存设置失败:', error)
        this.$message.error('保存设置失败: ' + error.message)
      }
    },

    // 拖动相关方法
    startResize(event) {
      this.isResizing = true
      this.startX = event.clientX
      this.startWidth = this.leftPanelWidth

      // 添加全局事件监听器
      document.addEventListener('mousemove', this.doResize)
      document.addEventListener('mouseup', this.stopResize)

      // 防止文本选择
      document.body.style.userSelect = 'none'
      document.body.style.cursor = 'col-resize'
    },

    doResize(event) {
      if (!this.isResizing) return

      const deltaX = event.clientX - this.startX
      const newWidth = this.startWidth + deltaX

      // 限制最小和最大宽度
      const minWidth = 280
      const maxWidth = window.innerWidth * 0.6 // 最大60%屏幕宽度

      this.leftPanelWidth = Math.max(minWidth, Math.min(maxWidth, newWidth))
    },

    stopResize() {
      this.isResizing = false

      // 移除全局事件监听器
      document.removeEventListener('mousemove', this.doResize)
      document.removeEventListener('mouseup', this.stopResize)

      // 恢复默认样式
      document.body.style.userSelect = ''
      document.body.style.cursor = ''

      // 保存面板宽度到本地存储
      localStorage.setItem('leftPanelWidth', this.leftPanelWidth.toString())
    },

    setTheme(theme) {
      console.log('🎨 setTheme 被调用，参数:', theme)
      console.log('🎨 当前store状态:', this.$store.state.theme)
      this.$store.commit('setTheme', theme)
      console.log('🎨 commit后store状态:', this.$store.state.theme)
      this.$store.dispatch('saveSettings')
      this.applyTheme()

      // 显示主题切换反馈
      this.$message.success(`主题已切换为: ${theme}`)
    },

    getSystemTheme() {
      if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        return 'dark'
      }
      return 'light'
    },

    setupSystemThemeListener() {
      if (window.matchMedia) {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
        mediaQuery.addEventListener('change', () => {
          if (this.currentTheme === 'auto') {
            this.applyTheme()
          }
        })
      }
    },

    applyTheme() {
      let themeToApply = 'light'

      if (this.currentTheme === 'dark') {
        themeToApply = 'dark'
      } else if (this.currentTheme === 'light') {
        themeToApply = 'light'
      } else if (this.currentTheme === 'auto') {
        themeToApply = this.getSystemTheme()
      }

      console.log('🎨 应用主题:', themeToApply, '当前主题设置:', this.currentTheme)

      // 应用主题到所有元素
      document.documentElement.setAttribute('data-theme', themeToApply)
      document.body.setAttribute('data-theme', themeToApply)

      // 移除所有主题类名
      document.body.classList.remove('dark-theme', 'light-theme', 'dark')
      document.documentElement.classList.remove('dark-theme', 'light-theme', 'dark')

      // 添加对应的主题类名
      if (themeToApply === 'dark') {
        document.body.classList.add('dark-theme', 'dark')
        document.documentElement.classList.add('dark-theme', 'dark')
      } else {
        document.body.classList.add('light-theme')
        document.documentElement.classList.add('light-theme')
      }

      // 强制重新渲染并验证
      this.$nextTick(() => {
        const actualTheme = document.documentElement.getAttribute('data-theme')
        console.log('✅ 主题应用完成，实际data-theme属性:', actualTheme)
        console.log('🔍 body背景色:', window.getComputedStyle(document.body).backgroundColor)

        // 更新窗口标题以显示当前主题（用于调试）
        if (window.electronAPI && window.electronAPI.setTitle) {
          window.electronAPI.setTitle(`Pixiv Spider - ${actualTheme} 主题`)
        }
      })
    },

    loadThemeSettings() {
      // 主题设置现在由store管理，在main.js中加载
      this.applyTheme()
    },

    // 窗口控制方法
    minimizeWindow() {
      if (window.electronAPI && window.electronAPI.minimizeWindow) {
        window.electronAPI.minimizeWindow()
      }
    },

    maximizeWindow() {
      if (window.electronAPI && window.electronAPI.maximizeWindow) {
        window.electronAPI.maximizeWindow()
      }
    },

    closeWindow() {
      if (window.electronAPI && window.electronAPI.closeWindow) {
        window.electronAPI.closeWindow()
      }
    }
  }
}
</script>

<style scoped>
/* 现代化基础布局 */
.app-container {
  height: 100vh;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
  overflow: hidden;
}

/* 现代化自定义标题栏样式 */
.custom-titlebar {
  height: 48px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  -webkit-app-region: drag;
  user-select: none;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

[data-theme="dark"] .custom-titlebar,
.dark-theme .custom-titlebar {
  background: rgba(15, 23, 42, 0.8);
}

.titlebar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 0 16px;
}

.titlebar-left {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 15px;
  font-weight: 600;
  color: var(--text-primary);
}

.app-icon {
  font-size: 20px;
  color: var(--primary-color);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.app-name {
  font-size: 15px;
  font-weight: 600;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--text-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.titlebar-center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-app-region: drag;
}

.titlebar-right {
  display: flex;
  align-items: center;
  gap: 12px;
  -webkit-app-region: no-drag;
}

.theme-switcher {
  margin: 0;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-xs);
}

.theme-switcher .el-button {
  border-radius: 0 !important;
  border: none !important;
  font-size: 12px !important;
  padding: 6px 12px !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.theme-switcher .el-button:first-child {
  border-top-left-radius: var(--radius-lg) !important;
  border-bottom-left-radius: var(--radius-lg) !important;
}

.theme-switcher .el-button:last-child {
  border-top-right-radius: var(--radius-lg) !important;
  border-bottom-right-radius: var(--radius-lg) !important;
}

.window-controls {
  display: flex;
  height: 100%;
  margin-left: 8px;
}

.window-control {
  width: 46px;
  height: 100%;
  border: none;
  background: transparent;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: var(--radius-sm);
  margin: 4px 2px;
  height: calc(100% - 8px);
}

.window-control:hover {
  background-color: var(--bg-tertiary);
  transform: scale(1.05);
}

.window-control.close:hover {
  background-color: var(--error-color);
  color: white;
}

/* 现代化主容器布局 */
.main-container {
  flex: 1;
  height: calc(100vh - 48px);
  display: flex;
  background: var(--bg-primary);
  position: relative;
}

/* 现代化配置面板样式 */
.config-panel {
  background: var(--bg-primary);
  border-radius: 0 var(--radius-xl) 0 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%;
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.config-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  z-index: 1;
}

.config-content {
  padding: 24px;
  height: 100%;
  overflow-y: auto;
}

.main-content {
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%;
  border-radius: var(--radius-xl) 0 0 0;
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
  border-left: 1px solid var(--border-color);
}

.main-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-success);
  z-index: 1;
}

.content-container {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.progress-section {
  margin-bottom: 24px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.log-section {
  flex: 1;
  min-height: 0;
  background: var(--bg-secondary);
  border-radius: var(--radius-xl);
  padding: 20px;
  box-shadow: var(--shadow-inner);
}

/* 可拖动分隔条样式 */
.resizer {
  width: 8px;
  background: var(--bg-primary);
  cursor: col-resize;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border-left: 1px solid var(--border-color);
  border-right: 1px solid var(--border-color);
}

.resizer:hover {
  background: var(--primary-light);
}

.resizer.resizing {
  background: var(--primary-color);
}

.resizer-line {
  width: 2px;
  height: 40px;
  background: var(--text-tertiary);
  border-radius: var(--radius-full);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.resizer:hover .resizer-line {
  background: var(--primary-color);
  height: 60px;
}

.resizer.resizing .resizer-line {
  background: white;
  height: 80px;
  box-shadow: var(--shadow-md);
}

/* 进度条现代化样式 */
.modern-progress .el-progress-bar__outer {
  background-color: var(--bg-tertiary) !important;
  border-radius: var(--radius-full) !important;
  overflow: hidden !important;
  height: 12px !important;
  box-shadow: var(--shadow-inner) !important;
}

.modern-progress .el-progress-bar__inner {
  background: var(--gradient-primary) !important;
  border-radius: var(--radius-full) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
}

.modern-progress .el-progress-bar__inner::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
  animation: progress-shine 2s ease-in-out infinite;
}

@keyframes progress-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Windows 11 风格现代化调整 */
.app-container {
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: var(--shadow-2xl);
  border: 1px solid var(--border-color);
}

/* 现代化响应式设计 */
@media (max-width: 1200px) {
  .progress-section {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .main-container {
    flex-direction: column;
    height: calc(100vh - 48px);
  }

  .config-panel {
    width: 100% !important;
    height: auto;
    border-radius: 0;
    max-height: 40vh;
  }

  .main-content {
    border-radius: 0;
    flex: 1;
    width: 100% !important;
    border-left: none;
  }

  .resizer {
    display: none; /* 在移动端隐藏拖动条 */
  }

  .config-content {
    padding: 16px;
  }

  .content-container {
    padding: 16px;
  }

  .titlebar-content {
    padding: 0 12px;
  }

  .titlebar-left {
    gap: 8px;
    font-size: 14px;
  }

  .app-icon {
    font-size: 18px;
  }

  .app-name {
    font-size: 14px;
  }

  .titlebar-right {
    gap: 8px;
  }

  .theme-switcher .el-button {
    font-size: 11px !important;
    padding: 4px 8px !important;
  }

  .window-control {
    width: 40px;
  }

  .progress-section {
    margin-bottom: 16px;
  }

  .log-section {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .custom-titlebar {
    height: 44px;
  }

  .main-container {
    height: calc(100vh - 44px);
  }

  .titlebar-left {
    font-size: 13px;
  }

  .app-icon {
    font-size: 16px;
  }

  .app-name {
    display: none; /* 在小屏幕上隐藏应用名称 */
  }

  .theme-switcher .el-button span {
    display: none; /* 只显示图标 */
  }

  .config-content,
  .main-content {
    padding: 12px;
  }

  .log-section {
    padding: 12px;
  }
}
</style>
